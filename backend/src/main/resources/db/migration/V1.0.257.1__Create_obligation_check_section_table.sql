-- Add comments to the table for schema clarity.
COMMENT ON TABLE public.packaging_service_criteria_option IS 'Join table for the many-to-many relationship between packaging_service and criteria_option.';

-- Step 1: Add the new column to the 'criteria_option' table.
-- The column is created as NULLABLE because a conditional follow-up question is optional
-- and only applies if the 'Show conditional' logic is triggered for a given criteria.
ALTER TABLE public.criteria_option
    ADD COLUMN optional_criteria_id INTEGER;

-- Step 2: Add the foreign key constraint to link 'criteria_option' back to the 'criteria' table.
-- ON DELETE RESTRICT prevents deleting a Criteria record if it is being used as a follow-up question by an option.
ALTER TABLE public.criteria_option
    ADD CONSTRAINT fk_criteria_option_on_optional_criteria
        FOREIGN KEY (optional_criteria_id)
            REFERENCES public.criteria (id)
            ON DELETE RESTRICT;

-- Step 3: Add a comment to describe the new column's purpose in the database schema.
COMMENT ON COLUMN public.criteria_option.optional_criteria_id IS 'Foreign key to the criteria table, used to link an option to a conditional follow-up question.';

-- Step 4: Create an index on the new foreign key column to improve query performance.
CREATE INDEX idx_criteria_option_optional_criteria_id ON public.criteria_option (optional_criteria_id);
