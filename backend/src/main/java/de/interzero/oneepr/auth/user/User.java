package de.interzero.oneepr.auth.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.auth.role.Role;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "one_epr_user",
        schema = "public"
)
public class User implements UserDetails {

    @JsonProperty("id")
    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "one_epr_user_id_gen"
    )
    @SequenceGenerator(
            name = "one_epr_user_id_gen",
            sequenceName = "one_epr_user_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonProperty("first_name")
    @Column(name = "first_name")
    private String firstName;

    @JsonProperty("last_name")
    @Column(name = "last_name")
    private String lastName;

    /**
     * @ts-legacy this field should be removed in the future
     */
    @JsonProperty("name")
    @Column(nullable = false)
    private String name;

    @JsonProperty("email")
    @Column(nullable = false)
    private String email;

    @JsonIgnore
    @Column
    private String password;

    @JsonProperty("is_active")
    @Column(
            name = "is_active",
            nullable = false
    )
    private Boolean isActive = false;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(
            name = "role_id",
            nullable = false
    )
    private Role role;

    @Transient
    @JsonProperty("role_id")
    public Integer getRoleId() {
        return role != null ? role.getId() : null;
    }

    @JsonProperty("token_verify")
    @Column(name = "token_verify")
    private String tokenVerify;

    @JsonProperty("token_expiration")
    @Column(name = "token_expiration")
    private LocalDateTime tokenExpiration;

    @JsonProperty("token_magic_link")
    @Column(name = "token_magic_link")
    private String tokenMagicLink;

    @JsonProperty("token_create_password")
    @Column(name = "token_create_password")
    private String tokenCreatePassword;

    @JsonProperty("token_attempts")
    @Column(
            name = "token_attempts",
            nullable = false
    )
    private Integer tokenAttempts = 0;

    @JsonProperty("block_time")
    @Column(name = "block_time")
    private LocalDateTime blockTime;

    @JsonProperty("status")
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status = Status.NOT_VERIFIED;

    @JsonProperty("created_at")
    @Column(name = "created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Column(name = "updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Column(name = "deleted_at")
    private Instant deletedAt;

    @OneToMany(
            mappedBy = "user",
            cascade = CascadeType.ALL
    )
    @JsonProperty("password_reset_requests")
    @JsonIgnore
    private transient List<PasswordResetRequest> passwordResetRequests;

    @OneToMany(
            mappedBy = "user",
            cascade = CascadeType.ALL
    )
    @JsonProperty("user_access_requests")
    @JsonIgnore
    private transient List<UserAccessRequest> userAccessRequests;

    @OneToMany(
            mappedBy = "user",
            cascade = CascadeType.ALL
    )
    @JsonProperty("change_user_emails")
    @JsonIgnore
    private transient List<ChangeUserEmail> changeUserEmails;

    @OneToOne(
            mappedBy = "user",
            cascade = CascadeType.ALL
    )
    @JsonProperty("refresh_token")
    @JsonIgnore
    private transient RefreshToken refreshToken;

    public enum Status {
        NOT_VERIFIED,
        VERIFIED_EMAIL,
        LOGIN,
        PENDING_PASSWORD,
        COMPLETE,
    }

    @PrePersist
    public void prePersist() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }

    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (role == null) {
            return List.of();
        } else {
            return List.of(new SimpleGrantedAuthority("ROLE_" + role.getName()));
        }
    }

    @Override
    public String getUsername() {
        return this.name;
    }

    @Override
    public boolean isAccountNonExpired() {
        return UserDetails.super.isAccountNonExpired();
    }

    @Override
    public boolean isAccountNonLocked() {
        return UserDetails.super.isAccountNonLocked();
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return UserDetails.super.isCredentialsNonExpired();
    }

    @Override
    public boolean isEnabled() {
        return this.status == Status.COMPLETE;
    }

    public enum Type {
        ADMIN,
        OPERATOR,
        CLIENT,
        COLLABORATOR,
        BILLING
    }

    public enum AccountStatus {
        ACTIVE,
        INACTIVE,
        BLOCKED,
        PENDING
    }

    public enum TypeResendToken {
        LOGIN,
        CREATE_ACCOUNT
    }


}
