package de.interzero.oneepr.auth.user;

import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.auth.auth.TokenService;
import de.interzero.oneepr.auth.auth.UrlAndTokenUtils;
import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.role.RoleRepository;
import de.interzero.oneepr.auth.user.dto.*;
import de.interzero.oneepr.auth.user.dto.email.EmailTokenDto;
import de.interzero.oneepr.customer.customer.CustomerService;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.customer.dto.CustomerProfileDto;
import de.interzero.oneepr.customer.customer.dto.UpdateCustomerDto;
import de.interzero.oneepr.customer.market_material.dto.SimpleUser;
import de.interzero.oneepr.customer.partner.Partner;
import de.interzero.oneepr.customer.partner.PartnerService;
import de.interzero.oneepr.customer.partner.dto.UpdatePartnerDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import jakarta.annotation.Nullable;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.util.UriUtils;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@SuppressWarnings("java:S6539")
@Slf4j
@Service
public class UserService {

    @Lazy
    private final CustomerService customerService;

    private final UserRepository userRepository;

    private final ChangeUserEmailRepository changeUserEmailRepository;

    private final PasswordResetRequestRepository passwordResetRequestRepository;

    private final UserAccessRequestRepository userAccessRequestRepository;

    private final RoleRepository roleRepository;

    private final EmailOutboxGateway emailOutboxGateway;

    private final PasswordEncoder passwordEncoder;

    private final TokenService tokenService;

    private final JwtEncoder jwtEncoder;

    private final JwtDecoder jwtDecoder;

    private final UrlAndTokenUtils urlAndTokenUtils;

    private final PartnerService partnerService;

    private static final String ERROR_ROLE_NOT_FOUND = "Role not found";

    private static final String ERROR_USER_NOT_FOUND = "User not found";

    private static final String EMAIL_SUBJECT_LIZENZERO = "Verify email Lizenzero";

    private static final String EMAIL_FROM_LIZENZERO = "Lizenzero <<EMAIL>>";

    private static final String TOKEN = "token";

    private static final String EMAIL = "email";

    private static final String MESSAGE = "message";

    private static final String MAGIC_LINK = "magicLink";

    private static final String MAGIC_LINK_CODE = "magicLinkCode";

    private static final String STATUS_PENDING = "PENDING";

    public UserService(@Lazy CustomerService customerService,
                       UserRepository userRepository,
                       ChangeUserEmailRepository changeUserEmailRepository,
                       PasswordResetRequestRepository passwordResetRequestRepository,
                       UserAccessRequestRepository userAccessRequestRepository,
                       RoleRepository roleRepository,
                       EmailOutboxGateway emailOutboxGateway,
                       PasswordEncoder passwordEncoder,
                       TokenService tokenService,
                       JwtEncoder jwtEncoder,
                       JwtDecoder jwtDecoder,
                       UrlAndTokenUtils urlAndTokenUtils,
                       PartnerService partnerService) {
        this.customerService = customerService;
        this.userRepository = userRepository;
        this.changeUserEmailRepository = changeUserEmailRepository;
        this.passwordResetRequestRepository = passwordResetRequestRepository;
        this.userAccessRequestRepository = userAccessRequestRepository;
        this.roleRepository = roleRepository;
        this.emailOutboxGateway = emailOutboxGateway;
        this.passwordEncoder = passwordEncoder;
        this.tokenService = tokenService;
        this.jwtEncoder = jwtEncoder;
        this.jwtDecoder = jwtDecoder;
        this.urlAndTokenUtils = urlAndTokenUtils;
        this.partnerService = partnerService;

        log.info("UserService constructor called");
    }


    /**
     * Find a user by ID or email.
     *
     * <p>This uses {@code findFirstByEmailOrId} which checks both email and ID.
     * If the input is numeric, it's treated as ID; otherwise, as email.</p>
     *
     * <p><b>Note:</b> If {@code fetch = FetchType.LAZY} is ever added to {@code User.role},
     * this method should be updated to use a custom query with {@code JOIN FETCH}
     * (e.g., {@code findFirstByEmailOrIdWithRole(...)}) or use {@code @EntityGraph}
     * to ensure the role is loaded.</p>
     *
     * @param idOrEmail user ID (as string) or email
     * @return user with role
     * @throws IllegalArgumentException if no user is found
     */
    public User findOne(String idOrEmail) {
        Integer id = null;
        String email = null;

        try {
            id = Integer.parseInt(idOrEmail);
        } catch (NumberFormatException e) {
            email = idOrEmail;
        }

        return userRepository.findFirstByEmailOrId(email, id)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID or email: " + idOrEmail));
    }


    /**
     * Find users filtered by name, is_active, ids or role.
     *
     * @param dto filter DTO
     * @return list of users
     */
    @SuppressWarnings("java:S3776")
    public List<User> findAll(UserFindAllDto dto) {
        List<String> roleNames;
        if (dto.getRole() != null && !dto.getRole().isBlank()) {
            roleNames = Arrays.stream(dto.getRole().split(",")).map(String::trim).map(String::toLowerCase).toList();
        } else {
            roleNames = null;
        }

        List<Integer> ids;
        if (dto.getIds() != null && !dto.getIds().isBlank()) {
            ids = Arrays.stream(dto.getIds().split(",")).map(String::trim).map(Integer::valueOf).toList();
        } else {
            ids = null;
        }

        Boolean isActive = dto.getIsActive() != null ? Boolean.parseBoolean(dto.getIsActive()) : null;

        String name = dto.getName() != null ? dto.getName().trim().toLowerCase() : null;

        Specification<User> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (name != null && !name.isBlank()) {
                String[] words = name.split("\\s+");
                for (String word : words) {
                    predicates.add(cb.like(cb.lower(root.get("name")), "%" + word + "%"));
                }
            }

            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), isActive));
            }

            if (ids != null && !ids.isEmpty()) {
                predicates.add(root.get("id").in(ids));
            }

            if (roleNames != null && !roleNames.isEmpty()) {
                Join<User, Role> roleJoin = root.join("role", JoinType.LEFT);
                predicates.add(cb.lower(roleJoin.get("name")).in(roleNames));
            }

            return predicates.isEmpty() ? cb.conjunction() : cb.and(predicates.toArray(new Predicate[0]));
        };

        return userRepository.findAll(spec, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    /**
     * Retrieve all active roles that are not marked as deleted.
     *
     * @return list of active {@link Role} entities
     */
    public List<Role> findAllRoles() {
        return roleRepository.findByIsActiveTrueAndDeletedAtIsNull();
    }

    /**
     * Create a user. If created by admin, activate and notify immediately.
     *
     * @param command          DTO with user creation data
     * @param createByAdminRaw flag indicating if the user is created by admin (as a string)
     * @return created user
     * @ts-legacy When creating a user, only the 'name' field is set. The 'firstName' and 'lastName' fields are not populated at this stage; 'name' contains the full name.
     */
    @Transactional(timeout = 20)
    @SuppressWarnings("java:S3776")
    public User create(CreateUserDto command,
                       String createByAdminRaw) {
        userRepository.findByEmailAndIsActiveTrueAndDeletedAtIsNull(command.getEmail()).ifPresent(user -> {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                    "User with email: " + command.getEmail() + " exists");
        });

        String originalPassword = command.getPassword();

        if (originalPassword != null && !originalPassword.isBlank()) {
            command.setPassword(passwordEncoder.encode(originalPassword));
        }

        boolean createByAdmin = Boolean.parseBoolean(createByAdminRaw);

        if (createByAdmin) {
            User newUser = new User();
            newUser.setName(command.getName());
            newUser.setEmail(command.getEmail());
            newUser.setPassword(command.getPassword());
            newUser.setIsActive(Boolean.TRUE.equals(command.getIsActive()));
            newUser.setTokenMagicLink(null);
            newUser.setTokenExpiration(null);
            newUser.setTokenVerify(null);

            if (command.getRoleId() != null) {
                Role role = roleRepository.findById(command.getRoleId())
                        .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, ERROR_ROLE_NOT_FOUND));
                newUser.setRole(role);
            }

            User createdUser = userRepository.save(newUser);

            if (Boolean.TRUE.equals(command.getIsActive())) {
                EmailMessage message = null;
                if (originalPassword != null) {
                    message = new EmailMessage(
                            "43", command.getEmail(), EMAIL_FROM_LIZENZERO, createdUser.getName(),
                            "User created successfully",
                            Map.of(
                                    EMAIL,
                                    command.getEmail(),
                                    "name",
                                    command.getName(),
                                    "password",
                                    originalPassword));
                }

                try {
                    emailOutboxGateway.sendEmail(message);
                } catch (Exception e) {
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
                }
            }

            return createdUser;
        }
        String token = urlAndTokenUtils.generateToken(5);
        String encryptedToken = passwordEncoder.encode(token);
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(1);

        Optional<User> existing = userRepository.findByEmailIgnoreCase(command.getEmail());

        User user = existing.orElseGet(User::new);
        user.setEmail(command.getEmail());
        user.setName(command.getName());
        user.setPassword(command.getPassword());
        user.setTokenVerify(encryptedToken);
        user.setTokenExpiration(expiresAt);
        user.setIsActive(false);
        user.setDeletedAt(null);

        if (command.getRoleId() != null) {
            Role role = roleRepository.findById(command.getRoleId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, ERROR_ROLE_NOT_FOUND));
            user.setRole(role);
        }

        user = userRepository.save(user);

        String baseMagicLink = command.getTokenMagicLink() != null ? command.getTokenMagicLink() : "";

        Map<String, String> magic = this.generateMagicLink(baseMagicLink, user.getId(), user.getEmail(), null);

        String magicLink = magic.get(MAGIC_LINK);
        String magicLinkCode = magic.get(MAGIC_LINK_CODE);

        Map<String, Object> messageData = (baseMagicLink.contains("/quick-journey") || baseMagicLink.contains(
                "/direct-license")) && baseMagicLink.contains("/create-account") ? Map.of(TOKEN, token) : Map.of(
                "callbackUrl",
                magicLinkCode);

        EmailMessage message = new EmailMessage(
                null, user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                EMAIL_SUBJECT_LIZENZERO,
                messageData);

        try {
            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
        }

        user.setTokenMagicLink(magicLink);
        user = userRepository.save(user);

        return user;

    }

    /**
     * Generate magic link and encoded JWT magic code for email verification.
     *
     * @param baseMagicLink base URL containing redirect path
     * @param userId        ID of the user
     * @param userEmail     email of the user
     * @param token         optional token; if null, a random one is generated
     * @return pair of magic link and magic code link
     */
    private Map<String, String> generateMagicLink(String baseMagicLink,
                                                  Integer userId,
                                                  String userEmail,
                                                  @Nullable String token) {
        String url = baseMagicLink != null ? baseMagicLink : "";

        URI uri = URI.create(url);
        String baseUrl = uri.getScheme() + "://" + uri.getHost();

        @SuppressWarnings("java:S2119") String magicToken = token != null ? token : urlAndTokenUtils.generateToken(20 + new Random().nextInt(
                21));

        String magicCode = jwtEncoder.encode(JwtEncoderParameters.from(JwtClaimsSet.builder()
                                                                               .claim(TOKEN, magicToken)
                                                                               .claim("id", userId)
                                                                               .claim(EMAIL, userEmail)
                                                                               .issuedAt(Instant.now())
                                                                               .expiresAt(Instant.now()
                                                                                                  .plus(
                                                                                                          1,
                                                                                                          ChronoUnit.HOURS))
                                                                               .build())).getTokenValue();

        String path = urlAndTokenUtils.extractPath(url);

        String magicLink = baseUrl + path + "/create-account?magic=" + magicToken + "&id=" + userId;
        String magicLinkCode = baseUrl + path + "/create-account?magic=" + magicCode;

        return Map.of(MAGIC_LINK, magicLink, MAGIC_LINK_CODE, magicLinkCode);
    }

    /**
     * Deletes the user by their ID.
     *
     * @param id the ID of the user to delete
     * @throws ResponseStatusException if user not found
     */
    public void remove(Integer id) {
        if (!userRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND);
        }

        userRepository.deleteById(id);
    }

    /**
     * Check if user exists and optionally if they have a password.
     *
     * @param id                   the ID of the user
     * @param checkUserHasPassword whether to check if user has a password
     * @return the found User
     * @throws ResponseStatusException if user not found or password missing
     */
    public User checkUserExist(Integer id,
                               boolean checkUserHasPassword) {
        User user = userRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        if (checkUserHasPassword && (user.getPassword() == null || user.getPassword().isBlank())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "User does not yet have a password");
        }

        return user;
    }

    /**
     * Find status and whether user has password by email.
     *
     * @param email email address of the user
     * @return map with "status" and "has_password" fields
     * @throws ResponseStatusException if user not found or inactive
     */
    public UserStatusResponse findStatusByEmail(String email) {
        Optional<User> optionalUser = userRepository.findFirstByEmailIgnoreCaseAndIsActiveTrueAndDeletedAtIsNull(email);

        if (optionalUser.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Email not found");
        }

        User user = optionalUser.get();
        boolean hasPassword = user.getPassword() != null && !user.getPassword().isBlank();

        return new UserStatusResponse(user.getStatus(), hasPassword);
    }

    /**
     * Update a user by ID.
     *
     * @param id            ID of the user to update
     * @param updateUserDto data to update
     * @return updated user without password
     * @throws ResponseStatusException if user not found or email already exists
     */
    public User update(Integer id,
                       UpdateUserDto updateUserDto) {
        if (updateUserDto.getPassword() != null) {
            String hashedPassword = passwordEncoder.encode(updateUserDto.getPassword());
            updateUserDto.setPassword(hashedPassword);
        }

        if (updateUserDto.getEmail() != null) {
            Optional<User> emailExists = userRepository.findFirstByEmailIgnoreCaseAndDeletedAtIsNull(updateUserDto.getEmail());

            if (emailExists.isPresent() && !emailExists.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Email already in use");
            }
        }

        User user = userRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        if (updateUserDto.getEmail() != null) {
            user.setEmail(updateUserDto.getEmail());
        }
        if (updateUserDto.getName() != null) {
            user.setName(updateUserDto.getName());
        }
        if (updateUserDto.getPassword() != null) {
            user.setPassword(updateUserDto.getPassword());
        }
        if (updateUserDto.getIsActive() != null) {
            user.setIsActive(updateUserDto.getIsActive());
        }
        if (updateUserDto.getRoleId() != null) {
            Role role = roleRepository.findById(updateUserDto.getRoleId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, ERROR_ROLE_NOT_FOUND));
            user.setRole(role);
        }
        if (updateUserDto.getTokenMagicLink() != null) {
            user.setTokenMagicLink(updateUserDto.getTokenMagicLink());
        }

        user = userRepository.save(user);

        return user;
    }

    /**
     * Update user's password after validating the old password.
     *
     * @param id                ID of the user
     * @param updatePasswordDto DTO containing old and new passwords
     * @return updated user (excluding password)
     * @throws ResponseStatusException if old password doesn't match or user not found
     */
    public User updatePassword(Integer id,
                               UpdatePasswordDto updatePasswordDto) {
        User user = this.checkUserExist(id, true);

        if (!passwordEncoder.matches(updatePasswordDto.getOldPassword(), user.getPassword())) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Unauthorized user");
        }

        String hashedPassword = passwordEncoder.encode(updatePasswordDto.getNewPassword());
        user.setPassword(hashedPassword);

        user = userRepository.save(user);

        EmailMessage message = new EmailMessage(
                "25", user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                "Password change confirmation",
                Map.of("name", user.getName()));

        try {
            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
        }

        return user;
    }

    /**
     * Update user's email by creating a ChangeUserEmail request and sending verification email.
     *
     * @param id             user ID
     * @param updateEmailDto new email + password confirmation
     * @return success message
     * @throws ResponseStatusException if unauthorized, email already exists, or email sending fails
     */
    public Map<String, String> updateEmail(Integer id,
                                           UpdateEmailDto updateEmailDto) {
        User user = this.checkUserExist(id, true);
        if (!passwordEncoder.matches(updateEmailDto.getPassword(), user.getPassword())) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Unauthorized user");
        }

        Optional<User> checkEmailAlready = userRepository.findFirstByEmailIgnoreCaseAndDeletedAtIsNull(updateEmailDto.getNewEmail());
        if (checkEmailAlready.isPresent()) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Email already");
        }

        changeUserEmailRepository.markDeletedByUser_Id(user.getId(), Instant.now());

        String token = urlAndTokenUtils.generateToken(5);
        String encryptedToken = passwordEncoder.encode(token);

        ChangeUserEmail changeRequest = new ChangeUserEmail();
        changeRequest.setUser(user);
        changeRequest.setNewEmail(updateEmailDto.getNewEmail());
        changeRequest.setToken(encryptedToken);
        changeUserEmailRepository.save(changeRequest);

        EmailMessage message = new EmailMessage(
                null,
                updateEmailDto.getNewEmail(),
                EMAIL_FROM_LIZENZERO,
                EMAIL_SUBJECT_LIZENZERO,
                user.getName(),
                Map.of("to", updateEmailDto.getNewEmail(), TOKEN, token, "subject", EMAIL_SUBJECT_LIZENZERO));

        try {
            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
        }

        return Map.of(MESSAGE, "Email exchange request made successfully");
    }

    /**
     * Verifies and updates user's email based on token and synchronizes it with external systems.
     *
     * @param id                   ID of the user
     * @param updateEmailVerifyDto verification data including token
     * @return updated user details (without password)
     * @throws ResponseStatusException if validation fails or external sync errors occur
     */
    @Transactional(timeout = 75)
    public User updateVerifyEmail(Integer id,
                                  UpdateEmailVerifyDto updateEmailVerifyDto) {
        User user = this.checkUserExist(id, false);

        ChangeUserEmail changeRequest = changeUserEmailRepository.findFirstByUser_IdAndDeletedAtIsNull(user.getId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Email change request not found"));

        Optional<User> existing = userRepository.findFirstByEmailIgnoreCaseAndDeletedAtIsNull(changeRequest.getNewEmail());
        if (existing.isPresent()) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Email already");
        }

        if (!passwordEncoder.matches(updateEmailVerifyDto.getToken(), changeRequest.getToken())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Invalid token");
        }

        if (Objects.equals(user.getRole().getName(), "CUSTOMER")) {
            AuthenticatedUser authUser = new AuthenticatedUser(
                    user.getId().toString(),
                    de.interzero.oneepr.customer.shared.auth.Role.CUSTOMER,
                    user.getEmail());

            CustomerProfileDto customer = customerService.findByUserId(user.getId(), authUser);

            UpdateCustomerDto dto = new UpdateCustomerDto();
            dto.setEmail(changeRequest.getNewEmail());

            customerService.update(customer.getId(), dto, authUser);
        }

        if (Objects.equals(user.getRole().getName(), "PARTNER")) {
            try {
                Partner partner = partnerService.findOneByEmail(user.getEmail());

                UpdatePartnerDto updateDto = new UpdatePartnerDto();
                updateDto.setPartnerEmail(changeRequest.getNewEmail());

                SimpleUser simpleUser = new SimpleUser(
                        user.getId().toString(),
                        de.interzero.oneepr.customer.shared.auth.Role.PARTNER.name());

                partnerService.update(partner.getId(), updateDto, simpleUser);

            } catch (ResponseStatusException e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getReason(), e);
            } catch (IOException e) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update partner", e);

            }
        }

        user.setEmail(changeRequest.getNewEmail());
        user = userRepository.save(user);

        try {
            EmailMessage message = new EmailMessage(
                    "26", user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                    "E-Mail change confirmation",
                    Map.of(EMAIL, user.getEmail()));
            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            log.warn("Failed to send confirmation email", e);
        }

        return user;
    }

    /**
     * Updates the user identified by their email address.
     *
     * @param email         the email address of the user
     * @param updateRequest the data to update
     * @return the updated User (without password)
     * @throws ResponseStatusException if email is missing or user not found
     */
    public User updateByEmail(String email,
                              UpdateUserDto updateRequest) {
        if (email == null || email.isBlank()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad Request");
        }

        User existingUser = userRepository.findByEmailAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        if (updateRequest.getPassword() != null) {
            String hashedPassword = passwordEncoder.encode(updateRequest.getPassword());
            existingUser.setPassword(hashedPassword);
        }

        if (updateRequest.getEmail() != null) {
            existingUser.setEmail(updateRequest.getEmail());
        }
        if (updateRequest.getName() != null) {
            existingUser.setName(updateRequest.getName());
        }
        if (updateRequest.getIsActive() != null) {
            existingUser.setIsActive(updateRequest.getIsActive());
        }
        if (updateRequest.getRoleId() != null) {
            Role role = roleRepository.findById(updateRequest.getRoleId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_ROLE_NOT_FOUND));
            existingUser.setRole(role);
        }
        if (updateRequest.getTokenMagicLink() != null) {
            existingUser.setTokenMagicLink(updateRequest.getTokenMagicLink());
        }

        return userRepository.save(existingUser);
    }

    /**
     * Handles a request to gain access for a user.
     *
     * <p>This method finds a user by their email or user ID, generates a one-time access token,
     * hashes it with bcrypt, stores the request in the database and constructs a callback URL.</p>
     *
     * <p>Mirrors the original Node.js logic {@code async requestAccess(dto: RequestAccessDto)}</p>
     *
     * @param dto Data Transfer Object containing the request details
     * @return RequestAccessResponse containing status, hashed token and callback URL
     * @throws ResponseStatusException if user is not found or required fields are missing
     */
    public RequestAccessResponse requestAccess(RequestAccessDto dto) {
        if ((dto.getEmail() == null || dto.getEmail().isBlank()) && (dto.getUserId() == null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Email or user_id is required");
        }

        User user = userRepository.findFirstByEmailOrId(dto.getEmail(), dto.getUserId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        String token = urlAndTokenUtils.generateToken(5);
        String encryptedToken = passwordEncoder.encode(token);

        UserAccessRequest request = new UserAccessRequest();
        request.setRequesterId(dto.getRequesterId());
        request.setToken(encryptedToken);
        request.setIsActive(true);
        request.setUser(user);
        request.setUserEmail(user.getEmail());
        request.setCallbackUrl(dto.getCallbackUrl());
        request.setStatus(STATUS_PENDING);
        request.setExpiresAt(Instant.now().plus(1, ChronoUnit.DAYS));

        userAccessRequestRepository.save(request);

        String divider = dto.getCallbackUrl().contains("?") ? "&" : "?";
        String callbackUrlWithToken = dto.getCallbackUrl() + divider + "token=" + token;

        RequestAccessResponse response = new RequestAccessResponse();
        response.setMessage("Email sent");
        response.setSuccess(true);
        response.setToken(encryptedToken);
        response.setCallbackUrl(callbackUrlWithToken);

        return response;
    }

    /**
     * Handles a password reset request by generating a reset token, saving it,
     * and triggering a password reset email through the email service.
     *
     * <p>Mirrors the original Node.js logic in {@code requestPasswordReset(email: string, callbackUrl: string)}</p>
     *
     * @param email       the email of the user requesting the password reset
     * @param callbackUrl the callback URL for the reset link
     * @return response with message and status
     */
    @Transactional
    public RequestAccessResponse requestPasswordReset(String email,
                                                      String callbackUrl) {
        if (email == null || email.isBlank()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Email is required");
        }

        User user = userRepository.findByEmailAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Email not found"));

        String token = passwordEncoder.encode(user.getEmail());
        Instant expiresAt = Instant.now().plus(1, ChronoUnit.HOURS);

        PasswordResetRequest resetRequest = new PasswordResetRequest();
        resetRequest.setUser(user);
        resetRequest.setToken(token);
        resetRequest.setCallbackUrl(callbackUrl);
        resetRequest.setStatus(STATUS_PENDING);
        resetRequest.setEmail(email);
        resetRequest.setExpiresAt(expiresAt);

        passwordResetRequestRepository.save(resetRequest);

        String urlWithParams = callbackUrl + "?token=" + UriUtils.encode(
                token,
                StandardCharsets.UTF_8) + "&email=" + email;

        try {
            String messageId = "clerk".equalsIgnoreCase(user.getRole().getName()) ? "32" : "3";

            EmailMessage message = new EmailMessage(
                    messageId, user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                    "Reset password Lizenzero",
                    Map.of("link_reset", urlWithParams));

            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            log.error("Failed to send forgot password email", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Erro ao enviar email", e);
        }

        RequestAccessResponse response = new RequestAccessResponse();
        response.setMessage("Email sent successfully");
        response.setSuccess(true);

        return response;
    }

    /**
     * Resets the password for a user based on a token (used for either user access or password reset).
     *
     * @param dto DTO containing the reset token, new password, and request type (USER_ACCESS or PASSWORD_RESET)
     * @return updated user (with password field nullified)
     * @throws ResponseStatusException if token is invalid or expired
     */
    @Transactional(timeout = 20)
    public User resetPassword(ResetPasswordDto dto) {
        String decodedToken = UriUtils.decode(dto.getToken(), StandardCharsets.UTF_8);
        Instant now = Instant.now();

        if ("USER_ACCESS".equalsIgnoreCase(dto.getType())) {
            UserAccessRequest request = userAccessRequestRepository.findFirstByTokenAndStatus(
                            decodedToken,
                            STATUS_PENDING)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request no longer valid"));

            if (request.getExpiresAt().isBefore(now)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request no longer valid");
            }

            User user = request.getUser();
            String hashedPassword = passwordEncoder.encode(dto.getPassword());
            user.setPassword(hashedPassword);
            userRepository.save(user);

            request.setStatus("DONE");
            request.setIsActive(false);
            userAccessRequestRepository.save(request);
            return user;

        } else {
            PasswordResetRequest request = passwordResetRequestRepository.findFirstByTokenAndStatus(
                            decodedToken,
                            STATUS_PENDING)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request no longer valid"));

            if (request.getExpiresAt().isBefore(now)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request no longer valid");
            }

            User user = request.getUser();
            String hashedPassword = passwordEncoder.encode(dto.getPassword());
            user.setPassword(hashedPassword);
            userRepository.save(user);

            request.setStatus("DONE");
            passwordResetRequestRepository.save(request);
            return user;
        }
    }

    /**
     * Sends a verification token via email with a magic link for user login.
     *
     * <p>Mirrors the original NestJS logic exactly. Finds an active user by email, generates a token,
     * builds a magic link, updates the user, and sends the email via admin email API.</p>
     *
     * @param email       target user's email
     * @param callbackUrl base callback URL for magic link
     * @return confirmation message
     */
    @Transactional
    public Map<String, String> sendVerificationToken(String email,
                                                     String callbackUrl) {
        User user = userRepository.findByEmailAndIsActiveTrueAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        String token = urlAndTokenUtils.generateToken(5);
        String encryptedToken = passwordEncoder.encode(token);

        LocalDateTime expiresAt = LocalDateTime.now().plusHours(1);

        Map<String, String> magic = this.generateMagicLink(callbackUrl, user.getId(), user.getEmail(), token);

        // Send email
        try {
            EmailMessage message = new EmailMessage(
                    "27", user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                    EMAIL_SUBJECT_LIZENZERO,
                    Map.of("link_reset", magic.get(MAGIC_LINK_CODE)));

            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            log.warn("Failed to send verification email", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to send magic link", e);
        }


        user.setTokenVerify(encryptedToken);
        user.setTokenExpiration(expiresAt);
        user.setTokenMagicLink(magic.get(MAGIC_LINK));
        user.setStatus(User.Status.LOGIN);

        userRepository.save(user);

        return Map.of(MESSAGE, "Token sent successfully");
    }

    /**
     * Confirms the email verification token and activates the user account.
     *
     * <p>This method validates the provided token, checks for expiration and blocking logic,
     * and updates the user's status accordingly. If the user has role ID 1 (customer),
     * it triggers customer creation in an external system.</p>
     *
     * @param dto email token DTO containing email and token
     * @return AuthResponse containing access token after successful verification
     * @throws ResponseStatusException if the token is invalid, expired, or the user is blocked
     */
    @SuppressWarnings("java:S3776")
    @Transactional(timeout = 20)
    public LoginResponse confirmVerificationToken(EmailTokenDto dto) {
        User user = userRepository.findFirstByEmailAndDeletedAtIsNullAndStatusIn(
                        dto.getEmail(),
                        List.of(
                                User.Status.NOT_VERIFIED,
                                User.Status.LOGIN))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Token not found"));

        if (user.getBlockTime() != null && urlAndTokenUtils.isValidDate(user.getBlockTime())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Temporarily blocked user");
        }

        if (!passwordEncoder.matches(dto.getToken(), user.getTokenVerify())) {
            if ((user.getTokenAttempts() != null ? user.getTokenAttempts() : 0) < 5) {
                user.setTokenAttempts((user.getTokenAttempts() != null ? user.getTokenAttempts() : 0) + 1);
                userRepository.save(user);
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Token invalid");
            }

            user.setBlockTime(LocalDateTime.now().plusMinutes(5));
            user.setTokenAttempts(0);
            userRepository.save(user);
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Temporarily blocked user");
        }

        if (!urlAndTokenUtils.isValidDate(user.getTokenExpiration())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Token expired");
        }

        user.setIsActive(true);
        user.setStatus(User.Status.VERIFIED_EMAIL);
        user.setTokenMagicLink(null);
        user.setTokenVerify(null);
        user.setTokenExpiration(LocalDateTime.now());

        User updatedUser = userRepository.save(user);

        if (updatedUser.getRole() != null && updatedUser.getRole().getId() == 4) {
            try {
                CreateCustomerDto customerDto = new CreateCustomerDto();
                customerDto.setFirstName(updatedUser.getName().split(" ")[0]);
                customerDto.setLastName(updatedUser.getName().contains(" ") ? updatedUser.getName()
                        .substring(updatedUser.getName().indexOf(" ") + 1) : "");
                customerDto.setEmail(updatedUser.getEmail());
                customerDto.setUserId(updatedUser.getId());

                customerService.create(customerDto);
            } catch (Exception e) {
                log.warn("Failed to sync customer for verified user", e);
            }
        }

        return tokenService.login(updatedUser, true);
    }

    /**
     * Resends a verification token and magic link to the specified email.
     *
     * @param email          the user's email
     * @param tokenMagicLink the base callback URL used for generating the magic link
     * @return a map with a success message
     */
    @Transactional(timeout = 20)
    public Map<String, String> resendVerificationToken(String email,
                                                       String tokenMagicLink) {
        User user = userRepository.findByEmailAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Email not found"));

        String token = urlAndTokenUtils.generateToken(5);
        String encryptedToken = passwordEncoder.encode(token);
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(1);

        Map<String, String> magic = this.generateMagicLink(tokenMagicLink, user.getId(), user.getEmail(), token);
        String magicLink = magic.get(MAGIC_LINK);
        String magicLinkCode = magic.get(MAGIC_LINK_CODE);

        user.setTokenVerify(encryptedToken);
        user.setTokenExpiration(expiresAt);
        user.setTokenMagicLink(magicLink);
        userRepository.save(user);

        try {
            boolean isCode = tokenMagicLink.contains("/quick-journey") || tokenMagicLink.contains("/direct-license");
            boolean isCreateAccount = tokenMagicLink.contains("/create-account");

            String templateId = isCode && isCreateAccount ? "9" : "5";

            Map<String, Object> messageData = isCode && isCreateAccount ? Map.of("verification_code", token) : Map.of(
                    "magic_link",
                    magicLinkCode);

            EmailMessage message = new EmailMessage(
                    templateId, user.getEmail(), EMAIL_FROM_LIZENZERO, user.getName(),
                    EMAIL_SUBJECT_LIZENZERO,
                    messageData);

            emailOutboxGateway.sendEmail(message);
        } catch (Exception e) {
            log.warn("Failed to send verification email", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to send verification email", e);
        }

        return Map.of(MESSAGE, "Token sent successfully");
    }

    /**
     * Confirms magic link verification and activates the user.
     *
     * @param dto MagicLinkDto containing the magic link
     * @return LoginResponse after successful verification
     */
    @SuppressWarnings("java:S3776")
    @Transactional(timeout = 20)
    public LoginResponse confirmVerificationMagicLink(MagicLinkDto dto) {
        String magicLink = dto.getMagicLink();

        if (magicLink == null || !magicLink.contains("?magic=")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad Request");
        }

        String[] magicSplit = magicLink.split("\\?magic=");
        if (magicSplit.length < 2) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad Request");
        }

        String onlyMagicCode = magicSplit[1];

        Jwt jwt;
        try {
            jwt = jwtDecoder.decode(onlyMagicCode);
        } catch (JwtException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid token");
        }

        Object userIdObj = jwt.getClaim("id");
        Object tokenObj = jwt.getClaim(TOKEN);

        if (userIdObj == null || tokenObj == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Token payload is incomplete");
        }

        int userId;
        try {
            userId = Integer.parseInt(userIdObj.toString());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid user ID");
        }

        String token = tokenObj.toString();

        String magicLinkComplete = magicSplit[0] + "?magic=" + token + "&id=" + userId;

        User user = userRepository.findByIdAndTokenMagicLink(userId, magicLinkComplete)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        if (user.getBlockTime() != null && urlAndTokenUtils.isValidDate(user.getBlockTime())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Temporarily blocked user");
        }

        if (!urlAndTokenUtils.isValidDate(user.getTokenExpiration())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Token expired");
        }

        user.setTokenMagicLink(null);
        user.setTokenVerify(null);
        user.setTokenExpiration(LocalDateTime.now());
        user.setIsActive(true);
        user.setStatus(User.Status.VERIFIED_EMAIL);

        User updatedUser = userRepository.save(user);

        if (updatedUser.getRole() != null && updatedUser.getRole().getId() == 1) {
            try {
                CreateCustomerDto createCustomerDto = new CreateCustomerDto();

                String[] nameParts = updatedUser.getName() != null ? updatedUser.getName()
                        .trim()
                        .split(" ") : new String[]{};
                createCustomerDto.setFirstName(nameParts.length > 0 ? nameParts[0] : "");
                createCustomerDto.setLastName(nameParts.length > 1 ? String.join(
                        " ",
                        Arrays.copyOfRange(
                                nameParts,
                                1,
                                nameParts.length)) : "");
                createCustomerDto.setEmail(updatedUser.getEmail());
                createCustomerDto.setUserId(updatedUser.getId());

                customerService.create(createCustomerDto);
            } catch (Exception e) {
                log.warn("Failed to sync customer for verified user", e);
            }
        }

        return tokenService.login(updatedUser, true);
    }

    /**
     * Sets a new password for a verified user who has no password set yet.
     *
     * @param dto CreatePasswordDto containing userId and new password
     * @return LoginResponse after setting the password
     */
    @Transactional
    public LoginResponse createPassword(CreatePasswordDto dto) {
        User user = userRepository.findByIdAndIsActiveTrueAndDeletedAtIsNull(dto.getUserId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Unauthorized action, user already has password");
        }

        String hashedPassword = passwordEncoder.encode(dto.getPassword());

        user.setPassword(hashedPassword);

        User updatedUser = userRepository.save(user);

        return tokenService.login(updatedUser, true);
    }

    /**
     * Checks if a user exists by email.
     *
     * @param email user's email
     * @return success message
     */
    public Map<String, String> getEmail(String email) {
        if (email == null || email.isBlank()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad request");
        }

        userRepository.findByEmailIgnoreCase(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_USER_NOT_FOUND));

        return Map.of(MESSAGE, "Request sent successfully");
    }

}
