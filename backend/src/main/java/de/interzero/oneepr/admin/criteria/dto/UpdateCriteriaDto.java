package de.interzero.oneepr.admin.criteria.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * DTO for partially updating an existing Criteria.
 * All fields are optional.
 */
@Getter
@Setter
public class UpdateCriteriaDto extends BaseDto {

    @Schema(
            description = "Mode of the criteria",
            implementation = Criteria.Mode.class
    )
    @JsonProperty("mode")
    private Criteria.Mode mode;

    @Schema(
            description = "Type of the criteria",
            implementation = Criteria.Type.class
    )
    @JsonProperty("type")
    private Criteria.Type type;

    @Schema(description = "Title of the criteria")
    @JsonProperty("title")
    private String title;

    @Schema(description = "Help text for the criteria")
    @JsonProperty("help_text")
    private String helpText;

    @Schema(
            description = "Input type of the criteria",
            implementation = Criteria.InputType.class
    )
    @JsonProperty("input_type")
    private Criteria.InputType inputType;

    @Schema(
            description = "Calculator type of the criteria",
            implementation = Criteria.CalculatorType.class
    )
    @JsonProperty("calculator_type")
    private Criteria.CalculatorType calculatorType;

    @Schema(description = "ID of the country")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "ID of the packaging service")
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @Schema(description = "ID of the required information")
    @JsonProperty("required_information_id")
    private Integer requiredInformationId;

    @Schema(
            description = "A list of options to create or update for this criteria. " + "Include an 'id' to update an existing option, or omit it to create a new one."
    )
    @JsonProperty("options")
    private List<UpdateCriteriaOptionDto> options;

    /**
     * Nested DTO representing a single option within a Criteria for update operations.
     */
    @Getter
    @Setter
    @Schema(description = "Represents a single option for a criteria during an update operation.")
    public static class UpdateCriteriaOptionDto {

        @Schema(description = "The ID of the option to update. Omit for new options.")
        @JsonProperty("id")
        private Integer id;

        @Schema(description = "ID of the parent criteria for this option")
        @JsonProperty("criteria_id")
        private Integer criteriaId;

        @Schema(description = "The start value of the option/range")
        @JsonProperty("option_value")
        private String optionValue;

        @Schema(description = "The end value of the option/range (nullable for single values)")
        @JsonProperty("option_to_value")
        private String optionToValue;

        @Schema(description = "The resulting value or outcome for this option")
        @JsonProperty("value")
        private String value;

        @Schema(description = "The packaging service id list")
        @JsonProperty("packaging_service_ids")
        private List<Integer> packagingServiceIds;

        @Schema(description = "The conditional criteria id")
        @JsonProperty("conditional_criteria_id")
        private Integer conditionalCriteriaId;
    }
}