package de.interzero.oneepr.admin.criteria;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.dto.CreateCriteriaDto;
import de.interzero.oneepr.admin.criteria.dto.CriteriaResponseDto;
import de.interzero.oneepr.admin.criteria.dto.UpdateCriteriaDto;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSection;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSectionRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import de.interzero.oneepr.admin.required_information.RequiredInformationRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CriteriaService {

    private final CriteriaMapper criteriaMapper;

    private final CriteriaRepository criteriaRepository;

    private final CriteriaOptionRepository criteriaOptionRepository;

    private final CountryRepository countryRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private final RequiredInformationRepository requiredInformationRepository;

    private final ObligationCheckSectionRepository obligationCheckSectionRepository;

    private static final String CRITERIA_NOT_FOUND = "Criteria not found";

    /**
     * Creates a new Criteria record along with its associated options within a single transaction.
     *
     * @param createDto The DTO containing data for the new criteria and its options.
     * @return The newly created Criteria entity, including its generated ID.
     * @ts-legacy The logic of creating the parent Criteria and then batch-creating the child Options
     * within a transaction is a direct translation of the original NestJS service.
     */
    @Transactional
    public CriteriaResponseDto create(CreateCriteriaDto createDto) {
        // 1. Create the parent entity and map ONLY its direct fields.
        Criteria criteria = new Criteria();
        criteria.setTitle(createDto.getTitle());
        criteria.setMode(createDto.getMode());
        criteria.setType(createDto.getType());
        criteria.setInputType(createDto.getInputType());
        criteria.setHelpText(createDto.getHelpText());
        criteria.setCalculatorType(createDto.getCalculatorType());

        // 2. Fetch and set the relationships for the parent entity.
        Country country = countryRepository.findById(createDto.getCountryId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));
        criteria.setCountry(country);

        if (createDto.getObligationCheckSectionId() != null) {
            ObligationCheckSection section = obligationCheckSectionRepository.findById(createDto.getObligationCheckSectionId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Section not found"));
            criteria.setObligationCheckSection(section);
        }
        if (createDto.getPackagingServiceId() != null) {
            PackagingService ps = packagingServiceRepository.findById(createDto.getPackagingServiceId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "PackagingService not found"));
            criteria.setPackagingService(ps);
        }
        if (createDto.getRequiredInformationId() != null) {
            RequiredInformation ri = requiredInformationRepository.findById(createDto.getRequiredInformationId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "RequiredInformation not found"));
            criteria.setRequiredInformation(ri);
        }

        // 3. Save the parent entity so it gets an ID and becomes managed.
        Criteria savedCriteria = criteriaRepository.save(criteria);

        // 4. Process the child DTO collection, creating new child entities.
        if (createDto.getOptions() != null && !createDto.getOptions().isEmpty()) {
            List<CriteriaOption> optionEntities = createDto.getOptions().stream().map(optionDto -> {
                CriteriaOption option = new CriteriaOption();
                option.setCriteria(savedCriteria); // Link to the MANAGED parent
                option.setOptionValue(optionDto.getOptionValue());
                option.setOptionToValue(optionDto.getOptionToValue());
                option.setValue(optionDto.getValue());

                if (optionDto.getConditionalCriteriaId() != null) {
                    Criteria optionalCriteria = criteriaRepository.findById(optionDto.getConditionalCriteriaId())
                            .orElseThrow(() -> new ResponseStatusException(
                                    HttpStatus.NOT_FOUND,
                                    "Optional criteria not found"));
                    option.setOptionalCriteria(optionalCriteria);
                }
                if (optionDto.getPackagingServiceIds() != null && !optionDto.getPackagingServiceIds().isEmpty()) {
                    List<PackagingService> packagingServices = packagingServiceRepository.findAllById(optionDto.getPackagingServiceIds());
                    option.setPackagingServices(new HashSet<>(packagingServices));
                }
                return option;
            }).toList();

            // 5. Batch-save the new child entities.
            List<CriteriaOption> savedOptions = criteriaOptionRepository.saveAll(optionEntities);

            // 6. Set the list of SAVED, correctly typed entities back onto the parent object.
            savedCriteria.setOptions(savedOptions);
        }

        // 7. Return the fully constructed and consistent parent object.
        return criteriaMapper.toDto(savedCriteria);
    }

    /**
     * Retrieves all active (non-soft-deleted) Criteria.
     *
     * @return A list of Criteria entities.
     */
    @Transactional(readOnly = true)
    public List<CriteriaResponseDto> findAll() {
        return criteriaRepository.findAllByDeletedAtIsNull().stream().map(criteriaMapper::toDto).toList();
    }

    /**
     * Finds a single active Criteria by its ID.
     *
     * @param id The ID of the criteria to find.
     * @return The found Criteria entity.
     * @throws ResponseStatusException if no active criteria is found for the given ID.
     */
    @Transactional(readOnly = true)
    public CriteriaResponseDto findOne(Integer id) {
        return criteriaMapper.toDto(criteriaRepository.findByIdAndDeletedAtIsNull(id)
                                            .orElseThrow(() -> new ResponseStatusException(
                                                    HttpStatus.NOT_FOUND,
                                                    CRITERIA_NOT_FOUND)));
    }

    /**
     * Updates an existing Criteria and its associated options.
     *
     * @param id        The ID of the criteria to update.
     * @param updateDto The DTO containing the fields to update.
     * @return The updated Criteria entity.
     * @ts-legacy The "delete-and-recreate" strategy for handling the 'options' update is a
     * direct translation of the original NestJS service logic to ensure equivalence.
     */
    @SuppressWarnings({"java:S3776"})
    @Transactional
    public CriteriaResponseDto update(Integer id,
                                      UpdateCriteriaDto updateDto) {
        Criteria criteria = criteriaRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CRITERIA_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, criteria);

        if (updateDto.getPresentFields().contains("country_id")) {
            Integer countryId = updateDto.getCountryId();
            if (countryId == null) {
                // The 'country' relationship is non-nullable on the Criteria entity.
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "country_id cannot be set to null.");
            }
            Country country = countryRepository.findById(countryId)
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "Country not found with id: " + countryId));
            criteria.setCountry(country);
        }

        if (updateDto.getPresentFields().contains("packaging_service_id")) {
            Integer psId = updateDto.getPackagingServiceId();
            if (psId != null) {
                PackagingService ps = packagingServiceRepository.findById(psId)
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                "PackagingService not found with id: " + psId));
                criteria.setPackagingService(ps);
            } else {
                criteria.setPackagingService(null);
            }
        }

        if (updateDto.getPresentFields().contains("required_information_id")) {
            Integer riId = updateDto.getRequiredInformationId();
            if (riId != null) {
                RequiredInformation ri = requiredInformationRepository.findById(riId)
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                "RequiredInformation not found with id: " + riId));
                criteria.setRequiredInformation(ri);
            } else {
                criteria.setRequiredInformation(null);
            }
        }

        criteria.setUpdatedAt(Instant.now());

        if (updateDto.getPresentFields().contains("options")) {
            criteriaOptionRepository.deleteByCriteriaId(id);

            List<CriteriaOption> newOptions = new ArrayList<>();
            if (updateDto.getOptions() != null && !updateDto.getOptions().isEmpty()) {
                newOptions = updateDto.getOptions().stream().map(optionDto -> {
                    CriteriaOption option = new CriteriaOption();
                    option.setCriteria(criteria);
                    option.setOptionValue(optionDto.getOptionValue());
                    option.setOptionToValue(optionDto.getOptionToValue());
                    option.setValue(optionDto.getValue());
                    option.setCreatedAt(Instant.now());
                    option.setUpdatedAt(Instant.now());
                    if (null != optionDto.getPackagingServiceIds()) {
                        List<PackagingService> packagingServices = packagingServiceRepository.findAllById(optionDto.getPackagingServiceIds());
                        option.setPackagingServices(new HashSet<>(packagingServices));
                    }
                    if (null != optionDto.getConditionalCriteriaId()) {
                        Criteria optionalCriteria = criteriaRepository.findById(optionDto.getConditionalCriteriaId())
                                .orElseThrow(() -> new ResponseStatusException(
                                        HttpStatus.NOT_FOUND,
                                        "Optional criteria not found"));
                        option.setOptionalCriteria(optionalCriteria);
                    }
                    return option;
                }).collect(Collectors.toCollection(ArrayList::new));
                criteriaOptionRepository.saveAll(newOptions);
            }
            criteria.setOptions(newOptions);
        }
        return criteriaMapper.toDto(criteriaRepository.save(criteria));
    }

    /**
     * Soft-deletes a Criteria by setting its 'deleted_at' timestamp.
     *
     * @param id The ID of the criteria to soft-delete.
     * @throws ResponseStatusException if no criteria is found for the given ID.
     */
    @Transactional
    public void remove(Integer id) {
        Criteria criteria = criteriaRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CRITERIA_NOT_FOUND));

        criteria.setDeletedAt(Instant.now());
        criteria.setUpdatedAt(Instant.now());
        criteriaRepository.save(criteria);
    }


    public List<CriteriaResponseDto> findAllWithSectionId(Long sectionId) {
        return criteriaRepository.findByObligationCheckSection_IdAndDeletedAtIsNull(sectionId)
                .stream()
                .map(criteriaMapper::toDto)
                .toList();
    }
}