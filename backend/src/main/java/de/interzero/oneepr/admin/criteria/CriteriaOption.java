package de.interzero.oneepr.admin.criteria;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(
        name = "criteria_option",
        schema = "public"
)
public class CriteriaOption {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "criteria_option_id_seq"
    )
    @SequenceGenerator(
            name = "criteria_option_id_seq",
            sequenceName = "criteria_option_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "value",
            nullable = false
    )
    @JsonProperty("value")
    private String value;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @Column(
            name = "option_value",
            nullable = false
    )
    @JsonProperty("option_value")
    private String optionValue;

    @Column(name = "option_to_value")
    @JsonProperty("option_to_value")
    private String optionToValue;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "criteria_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("criteria_id")
    private Criteria criteria;

    @ManyToMany(
            cascade = {CascadeType.PERSIST, CascadeType.MERGE},
            fetch = FetchType.LAZY
    )
    @JoinTable(
            name = "packaging_service_criteria_option",
            joinColumns = @JoinColumn(name = "criteria_option_id"),
            inverseJoinColumns = @JoinColumn(name = "packaging_service_id")
    )
    private Set<PackagingService> packagingServices = new HashSet<>();

    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "optional_criteria_id",
            unique = true
    )
    @JsonIgnore
    private Criteria optionalCriteria;

    @Transient
    @JsonProperty("criteria_id")
    public Integer getCriteriaId() {
        return (this.criteria != null) ? this.criteria.getId() : null;
    }

    /**
     * Exposes the ID of the related conditional criteria for JSON serialization.
     *
     * @return The ID of the optional (conditional) criteria, or null if not set.
     */
    @Transient
    @JsonProperty("conditional_criteria_id")
    public Integer getConditionalCriteriaId() {
        return (this.optionalCriteria != null) ? this.optionalCriteria.getId() : null;
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }
}