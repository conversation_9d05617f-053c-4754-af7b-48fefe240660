package de.interzero.oneepr.admin.mail;

import de.interzero.oneepr.customer.http.AdminInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Customer.io implementation of {@link EmailOutboxGateway}.
 * <p>
 * Sends an email via the admin service, which proxies the request to Customer.io.
 * Uses the {@link AdminInterface} placeholder method to send POST request with email payload.
 * <p>
 * This implementation is used in production when mail.gateway != sandbox.
 */
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
        prefix = "mail",
        name = "gateway",
        havingValue = "customerio",
        matchIfMissing = true
)
@Slf4j
public class CustomerIoEmailGateway implements EmailOutboxGateway {

    private final RestTemplate restTemplate;

    @Value("${customerio.api-key}")
    private String customerIoApiKey;

    @Value("${customerio.region:eu}")
    private String customerIoRegion;

    /**
     * Sends a transactional email via the Customer.io API.
     * <p>
     * Constructs the required payload from the provided {@link EmailMessage} and sends it to the Customer.io
     * transactional email endpoint using the configured API key for authentication.
     * <p>
     * The method sets required fields (transactional_message_id, to, from, identifiers) and optional fields
     * (subject, recipient_name, message_data) as per Customer.io API specification.
     *
     * @param emailMessage the email message containing all necessary data for sending
     * @throws EmailDeliveryException if the email could not be sent or the API call fails
     */
    @Override
    public void sendEmail(EmailMessage emailMessage) {
        try {
            String url = buildApiUrl();
            Map<String, Object> payload = new HashMap<>();
            payload.put("transactional_message_id", emailMessage.getTransactionalMessageId());
            payload.put("to", emailMessage.getTo());
            payload.put("from", emailMessage.getFrom());

            if (emailMessage.getSubject() != null) {
                payload.put("subject", emailMessage.getSubject());
            }

            Map<String, Object> identifiers = new HashMap<>();
            identifiers.put("email", emailMessage.getTo());
            payload.put("identifiers", identifiers);

            Map<String, Object> messageData = emailMessage.getMessageData() != null ? new HashMap<>(emailMessage.getMessageData()) : new HashMap<>();
            if (emailMessage.getRecipientName() != null) {
                messageData.remove("name");
                messageData.put("recipient_name", emailMessage.getRecipientName());
            }
            payload.put("message_data", messageData);


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(customerIoApiKey);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(payload, headers);

            ResponseEntity<Void> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                log.info("Email sent via Customer.io: {}", emailMessage.getTransactionalMessageId());
            }
        } catch (Exception e) {
            log.error("Failed to send email via Customer.io", e);
            throw new EmailDeliveryException("Failed to send email via Customer.io", e);
        }
    }

    /**
     * Builds the Customer.io API URL based on the configured region.
     * <p>
     * Customer.io has different API endpoints for different regions:
     * - US: api.customer.io
     * - EU: api-eu.customer.io
     * 
     * @return the complete API URL for sending transactional emails
     */
    private String buildApiUrl() {
        String baseUrl = "https://api-eu.customer.io";
        if ("us".equalsIgnoreCase(customerIoRegion)) {
            baseUrl = "https://api.customer.io";
        }
        return baseUrl + "/v1/send/email";
    }
}
