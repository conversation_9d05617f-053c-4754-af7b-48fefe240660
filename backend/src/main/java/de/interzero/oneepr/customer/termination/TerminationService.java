package de.interzero.oneepr.customer.termination;

import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.action_guide.ActionGuideRepository;
import de.interzero.oneepr.admin.mail.EmailDeliveryException;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.file.FileRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.reason.TerminationReason;
import de.interzero.oneepr.customer.reason.TerminationReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.termination.dto.CreateTerminationDto;
import de.interzero.oneepr.customer.termination.dto.UpdateTerminationDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;

/**
 * Service for managing contract terminations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TerminationService {

    private final TerminationRepository terminationRepository;

    private final TerminationReasonRepository terminationReasonRepository;

    private final ContractRepository contractRepository;

    private final ReasonRepository reasonRepository;

    private final FileRepository fileRepository;

    private final LicenseRepository licenseRepository;

    private final ActionGuideRepository actionGuideRepository;

    private static final String TERMINATION_NOT_FOUND = "Termination not found";

    private static final String MESSAGE = "message";

    @Value("${app.core-api-url}")
    private String coreApiUrl;

    private final EmailOutboxGateway emailOutboxGateway;

    private static final String EMAIL_ADDRESS = "Lizenzero <<EMAIL>>";

    private final SimpleDateFormat dateFormatter = new SimpleDateFormat("dd.MM.yyyy");

    /**
     * Finds a single termination by its ID, after validating user permissions.
     *
     * @param id   The ID of the termination to retrieve.
     * @param user The authenticated user making the request.
     * @return The found Termination entity with its relations.
     */
    @Transactional(readOnly = true)
    public Termination findById(Integer id,
                                AuthenticatedUser user) {
        validateUserPermission(id, user);
        return terminationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, TERMINATION_NOT_FOUND));
    }


    /**
     * Creates a new termination request based on the provided data.
     *
     * @param dto The DTO containing the termination details.
     * @return A map with a success message.
     */
    @Transactional
    public Map<String, String> create(CreateTerminationDto dto) {
        Instant now = Instant.now();

        Contract contract = contractRepository.findWithCustomerByIdAndDeletedAtIsNull(dto.getContractId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found"));

        if (contract.getStatus() != Contract.Status.ACTIVE) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Contract is already terminated or in termination process");
        }
        if (dto.getCountryCodes() != null && dto.getCountryCodes().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "If provided, country codes cannot be empty");
        }
        if (dto.getReasonIds() == null || dto.getReasonIds().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Reason IDs are required");
        }

        Termination termination = new Termination();
        termination.setRequestedAt(now);
        termination.setStatus(dto.getStatus() != null ? dto.getStatus() : Termination.Status.REQUESTED);
        termination.setCreatedAt(now);
        termination.setUpdatedAt(now);
        List<Reason> reasons = reasonRepository.findAllById(dto.getReasonIds());
        for (Reason reason : reasons) {
            TerminationReason tr = new TerminationReason(null, now, now, null, termination, reason);
            termination.getReasons().add(tr);
        }
        Termination savedTermination = terminationRepository.save(termination);

        if (dto.getTerminationFileId() != null) {
            fileRepository.findById(dto.getTerminationFileId()).ifPresent(file -> {
                file.setTermination(savedTermination);
                fileRepository.save(file);
            });
        }

        if (contract.getType() == Contract.Type.ACTION_GUIDE) {
            updateActionGuidesForTermination(dto, savedTermination, contract, now);
        } else {
            updateLicensesForTermination(dto, savedTermination, contract, now);
        }
        sendTerminationEmail(savedTermination.getId(), contract);

        return Collections.singletonMap(MESSAGE, "Termination created successfully");
    }

    /**
     * Constructs and sends the termination request email.
     */
    private void sendTerminationEmail(Integer terminationId,
                                      Contract contract) {
        Termination termination = terminationRepository.findById(terminationId).orElse(null);

        if (termination == null || contract.getCustomer() == null || contract.getCustomer().getEmail() == null) {
            log.warn("Could not send termination email. Missing termination, customer, or email.");
            return;
        }

        Map<String, Object> messageData = new HashMap<>();
        messageData.put(
                "termination_date_request",
                termination.getRequestedAt() != null ? dateFormatter.format(Date.from(termination.getRequestedAt())) : null);
        messageData.put(
                "termination_date",
                !termination.getContracts().isEmpty() && termination.getContracts()
                                                                 .getFirst()
                                                                 .getEndDate() != null ? dateFormatter.format(Date.from(
                        termination.getContracts().getFirst().getEndDate())) : null);
        messageData.put(
                "termination_reasons",
                termination.getReasons().stream().map(tr -> tr.getReason().getTitle()).toList());
        messageData.put(
                "url_download_attachment",
                !termination.getFiles().isEmpty() ? coreApiUrl + "/customer/files/" + termination.getFiles()
                        .getFirst()
                        .getId() : null);
        messageData.put("termination_countries", contract.getLicenses().stream().map(License::getCountryName).toList());


        EmailMessage emailMessage = new EmailMessage(
                "23",
                                                     contract.getCustomer().getEmail(),
                                                     EMAIL_ADDRESS,
                                                     "Termination request",
                                                     messageData);
        emailMessage.setRecipientName(contract.getCustomer().getFirstName(), contract.getCustomer().getLastName());

        try {
            emailOutboxGateway.sendEmail(emailMessage);
        } catch (EmailDeliveryException e) {
            log.error("FAILED TO SEND EMAIL TERMINATION REQUEST: {}", e.getMessage());
        }
    }


    /**
     * Updates a termination request, branching logic based on the new status.
     * This includes updating related entities and sending conditional email notifications.
     *
     * @param id   The ID of the termination to update.
     * @param dto  The DTO containing the update data.
     * @param user The authenticated user.
     * @return A map with a success message.
     */
    @Transactional
    public Map<String, String> update(Integer id,
                                      UpdateTerminationDto dto,
                                      AuthenticatedUser user) {
        Instant now = Instant.now();

        Termination termination = terminationRepository.findWithAllUpdateRelationsById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, TERMINATION_NOT_FOUND));

        validateUserPermission(id, user);

        if (dto.getProofOfTerminationFileId() != null) {
            fileRepository.findById(dto.getProofOfTerminationFileId()).ifPresent(file -> {
                file.setTermination(termination);
                fileRepository.save(file);
            });
        }

        boolean isActionGuideTermination = termination.getContracts()
                                                   .stream()
                                                   .anyMatch(c -> c.getType() == Contract.Type.ACTION_GUIDE) || !termination.getActionGuides()
                .isEmpty();

        if (dto.getStatus() == Termination.Status.PENDING && !isActionGuideTermination) {
            termination.setStatus(Termination.Status.PENDING);
            termination.setUpdatedAt(now);
            terminationRepository.save(termination);

        } else if (dto.getStatus() == Termination.Status.COMPLETED || isActionGuideTermination) {
            termination.setStatus(Termination.Status.COMPLETED);
            termination.setCompletedAt(now);
            termination.setUpdatedAt(now);
            terminationRepository.save(termination);

            updateRelatedToCompleted(termination, now);

            if (!termination.getContracts().isEmpty() && termination.getContracts()
                                                                 .getFirst()
                                                                 .getCustomer() != null && isActionGuideTermination) {
                String customerEmail = termination.getContracts().getFirst().getCustomer().getEmail();
                String customerFirstName = termination.getContracts().getFirst().getCustomer().getFirstName();
                String customerLastName = termination.getContracts().getFirst().getCustomer().getLastName();
                sendCompletedActionGuideEmail(customerEmail, customerFirstName, customerLastName);
            }
        }

        return Collections.singletonMap(MESSAGE, "Termination updated successfully");
    }

    /**
     * Sends the email for completed Action Guide terminations.
     */
    private void sendCompletedActionGuideEmail(String customerEmail,
                                               String customerFirstName,
                                               String customerLastName) {
        if (customerEmail == null || customerEmail.isEmpty()) {
            return;
        }

        EmailMessage emailMessage = new EmailMessage(
                "20", customerEmail, EMAIL_ADDRESS,
                                                     "Termination confirmation",
                                                     new HashMap<>());
        emailMessage.setRecipientName(customerFirstName, customerLastName);
        try {
            emailOutboxGateway.sendEmail(emailMessage);
        } catch (EmailDeliveryException e) {
            log.error("Failed to send completed action guide termination email: {}", e.getMessage());
        }
    }

    /**
     * Revokes a termination request, reactivating associated entities and deleting the termination record.
     *
     * @param id   The ID of the termination to revoke.
     * @param user The authenticated user.
     * @return A map with a success message.
     */
    @SuppressWarnings("java:S3776")
    @Transactional
    public Map<String, String> revoke(Integer id,
                                      AuthenticatedUser user) {
        Termination termination = terminationRepository.findWithRevokeRelationsById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, TERMINATION_NOT_FOUND));

        validateUserPermission(id, user);

        Instant now = Instant.now();
        String customerEmail = null;
        String contractName = null;
        String customerFirstName = null;
        String customerLastName = null;

        // This logic now mirrors the TS version's independent checks.
        if (!termination.getContracts().isEmpty()) {
            Contract contract = termination.getContracts().getFirst();
            if (now.isAfter(contract.getEndDate())) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "You can't revoke the termination after the contract cancellation date");
            }

            customerEmail = (contract.getCustomer() != null) ? contract.getCustomer().getEmail() : null;
            customerFirstName = (contract.getCustomer() != null) ? contract.getCustomer().getFirstName() : null;
            customerLastName = (contract.getCustomer() != null) ? contract.getCustomer().getLastName() : null;
            contractName = contract.getTitle();

            contract.setStatus(Contract.Status.ACTIVE);
            contract.setUpdatedAt(now);

            // Replicate filtered include for non-deleted licenses/guides
            List<Integer> licenseIds = contract.getLicenses()
                    .stream()
                    .filter(l -> l.getDeletedAt() == null)
                    .map(License::getId)
                    .toList();
            if (!licenseIds.isEmpty()) {
                licenseRepository.reactivateAllByIds(licenseIds, now);
            }

            List<Integer> actionGuideIds = contract.getActionGuides()
                    .stream()
                    .filter(ag -> ag.getDeletedAt() == null)
                    .map(ActionGuide::getId)
                    .toList();
            if (!actionGuideIds.isEmpty()) {
                actionGuideRepository.reactivateAllByIds(actionGuideIds, now);
            }
        }

        // This block handles terminations linked directly to licenses (not via a master contract)
        if (!termination.getLicenses().isEmpty()) {
            termination.getLicenses().forEach(license -> {
                if (now.isBefore(license.getContract().getEndDate())) {
                    license.setContractStatus(License.ContractStatus.ACTIVE);
                    license.setUpdatedAt(now);
                } else {
                    throw new ResponseStatusException(
                            HttpStatus.BAD_REQUEST,
                            "You can't revoke the termination after the contract cancellation date");
                }
            });
        }

        // This block handles terminations linked directly to action guides
        if (!termination.getActionGuides().isEmpty()) {
            termination.getActionGuides().forEach(guide -> {
                if (now.isBefore(guide.getContract().getEndDate())) {
                    guide.setContractStatus(Contract.Status.ACTIVE);
                    guide.setUpdatedAt(now);
                } else {
                    throw new ResponseStatusException(
                            HttpStatus.BAD_REQUEST,
                            "You can't revoke the termination after the contract cancellation date");
                }
            });
        }

        // Delete all reasons and then the termination record itself
        terminationReasonRepository.deleteAllByTerminationId(id);
        terminationRepository.deleteById(id);

        // Send final notification email
        if (customerEmail != null) {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("contract_name", contractName);
            messageData.put("contract_status", "ACTIVE");

            EmailMessage emailMessage = new EmailMessage(
                    "12", customerEmail, EMAIL_ADDRESS,
                                                         "Contract status changed",
                                                         messageData);
            emailMessage.setRecipientName(customerFirstName, customerLastName);
            try {
                emailOutboxGateway.sendEmail(emailMessage);
            } catch (EmailDeliveryException e) {
                log.error("Failed to send termination revoked email: {}", e.getMessage());
            }
        }

        return Collections.singletonMap(MESSAGE, "Termination revoked successfully");
    }

    private void updateActionGuidesForTermination(CreateTerminationDto dto,
                                                  Termination termination,
                                                  Contract contract,
                                                  Instant now) {
        List<ActionGuide> guides = actionGuideRepository.findAllByContract_IdAndDeletedAtIsNull(contract.getId());
        List<ActionGuide> guidesToTerminate = (dto.getCountryCodes() == null) ? guides : guides.stream()
                .filter(g -> dto.getCountryCodes().contains(g.getCountryCode()))
                .toList();

        guidesToTerminate.forEach(guide -> {
            guide.setContractStatus(Contract.Status.TERMINATION_PROCESS);
            guide.setTermination(termination);
            guide.setUpdatedAt(now);
        });
        actionGuideRepository.saveAll(guidesToTerminate);

        if (guidesToTerminate.size() == guides.size()) {
            contract.setStatus(Contract.Status.TERMINATION_PROCESS);
            contract.setTermination(termination);
            contract.setUpdatedAt(now);
            contractRepository.save(contract);
        }
    }

    private void updateLicensesForTermination(CreateTerminationDto dto,
                                              Termination termination,
                                              Contract contract,
                                              Instant now) {
        List<License> licenses = licenseRepository.findAllByContractIdAndDeletedAtIsNull(contract.getId());
        List<License> licensesToTerminate = (dto.getCountryCodes() == null) ? licenses : licenses.stream()
                .filter(l -> dto.getCountryCodes().contains(l.getCountryCode()))
                .toList();

        licensesToTerminate.forEach(license -> {
            license.setContractStatus(License.ContractStatus.TERMINATION_PROCESS);
            license.setTermination(termination);
            license.setUpdatedAt(now);
        });
        licenseRepository.saveAll(licensesToTerminate);
        licensesToTerminate.forEach(license -> {
            if (license.getRegistrationAndTerminationMondayRef() != null && license.getRegistrationStatus() != null) {
                licenseRepository.updateRegistrationStatusByMondayRef(
                        license.getRegistrationAndTerminationMondayRef(),
                        license.getRegistrationStatus());
            }
        });
        if (licensesToTerminate.size() == licenses.size()) {
            contract.setStatus(Contract.Status.TERMINATION_PROCESS);
            contract.setTermination(termination);
            contract.setUpdatedAt(now);
            contractRepository.save(contract);
        }
    }

    private void updateRelatedToCompleted(Termination termination,
                                          Instant now) {
        // Update associated contracts
        termination.getContracts().forEach(c -> {
            c.setStatus(Contract.Status.TERMINATED);
            c.setUpdatedAt(now);
        });
        contractRepository.saveAll(termination.getContracts());

        // Update associated licenses
        termination.getLicenses().forEach(l -> {
            l.setContractStatus(License.ContractStatus.TERMINATED);
            l.setUpdatedAt(now);
        });
        licenseRepository.saveAll(termination.getLicenses());

        // Update associated action guides
        termination.getActionGuides().forEach(ag -> {
            ag.setContractStatus(Contract.Status.TERMINATED);
            ag.setUpdatedAt(now);
        });
        actionGuideRepository.saveAll(termination.getActionGuides());
    }


    /**
     * Validates that a user has permission to access a given Termination.
     * <p>
     * This method finds the associated customer by checking three different relational paths
     * in order: through a direct contract, through a terminated license, or through a
     * terminated action guide. It then validates the user's ownership.
     *
     * @param id   The ID of the Termination.
     * @param user The user to validate.
     * @throws ResponseStatusException if the termination, or its associated customer, cannot be found,
     *                                 or if the user lacks permission.
     */
    private void validateUserPermission(Integer id,
                                        AuthenticatedUser user) {
        Termination termination = terminationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, TERMINATION_NOT_FOUND));

        Customer customer = null;

        if (!termination.getContracts().isEmpty() && termination.getContracts().getFirst().getCustomer() != null) {
            customer = termination.getContracts().getFirst().getCustomer();
        } else if (!termination.getLicenses().isEmpty() && termination.getLicenses().getFirst().getContract() != null) {
            customer = termination.getLicenses().getFirst().getContract().getCustomer();
        } else if (!termination.getActionGuides().isEmpty() && termination.getActionGuides()
                                                                                                                                                                                                                                                                                                                .getFirst()
                                                                                                                                                                                                                                                                                                                .getContract() != null) {
            customer = termination.getActionGuides().getFirst().getContract().getCustomer();
        }

        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found for this termination");
        }

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().toString().equals(user.getId())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You are not allowed to access this termination");
        }
    }
}
