package de.interzero.oneepr.customer.company;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.customer.company.dto.*;
import de.interzero.oneepr.customer.company_billing.CompanyBilling;
import de.interzero.oneepr.customer.company_billing.CompanyBillingRepository;
import de.interzero.oneepr.customer.company_email.CompanyEmail;
import de.interzero.oneepr.customer.company_email.CompanyEmailRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.customer_phone.CustomerPhone;
import de.interzero.oneepr.customer.customer_phone.CustomerPhoneRepository;
import de.interzero.oneepr.customer.entity.CompanyAddress;
import de.interzero.oneepr.customer.entity.CompanyAddressRepository;
import de.interzero.oneepr.customer.entity.CompanyContact;
import de.interzero.oneepr.customer.integration.MondayService;
import de.interzero.oneepr.customer.partner.Partner;
import de.interzero.oneepr.customer.partner.PartnerRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for company operations.
 * Converted from TypeScript CompanyService with exact same method names and logic.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompanyService {

    private static final String INVALID = "INVALID";

    private final CompanyRepository companyRepository;

    private final CompanyEmailRepository companyEmailRepository;

    private final CompanyContactRepository companyContactRepository;

    private final CompanyAddressRepository companyAddressRepository;

    private final CompanyBillingRepository companyBillingRepository;

    private final CustomerRepository customerRepository;

    private final CustomerPhoneRepository customerPhoneRepository;

    private final PartnerRepository partnerRepository;

    private final CustomerIoService customerIoService;

    private final MondayService mondayService;

    private final VatValidationService vatValidationService;

    private final EmailOutboxGateway emailOutboxGateway;

    private final ObjectMapper objectMapper;

    @Value("${app.crm.api.url:}")
    private String crmApiUrl;

    @Value("${app.master.vat.id:}")
    private String masterVatId;

    /**
     * Find company by document (VAT or TIN).
     * Equivalent to TypeScript: findByDocument(vat?, tin?)
     *
     * @param vat VAT number (optional)
     * @param tin TIN number (optional)
     * @return Company or null
     */
    public Company findByDocument(String vat,
                                  String tin) {
        Company company = null;

        if (vat != null && !vat.isEmpty()) {
            Optional<Company> companyOpt = companyRepository.findByVat(vat);
            if (companyOpt.isPresent()) {
                company = companyOpt.get();
            }
        }

        if (tin != null && !tin.isEmpty()) {
            Optional<Company> companyOpt = companyRepository.findByTin(tin);
            if (companyOpt.isPresent()) {
                company = companyOpt.get();
            }
        }

        return company;
    }

    /**
     * Find company by LUCID number.
     * Equivalent to TypeScript: findByLucid(lucid)
     *
     * @param lucid LUCID number
     * @return Company or null
     */
    public Company findByLucid(String lucid) {
        Optional<Company> companyOpt = companyRepository.findByLucid(lucid);
        return companyOpt.orElse(null);
    }

    /**
     * Add customer phones and company emails.
     * Equivalent to TypeScript: addCustomerMailsAndPhones(addCustomerMailsAndPhones)
     *
     * @param addCustomerMailsAndPhones DTO with emails and phones to add
     * @ts-legacy not used in the final tag
     */
    @Transactional
    public void addCustomerMailsAndPhones(AddCustomerMailsAndPhonesDto addCustomerMailsAndPhones) {
        try {
            // Create company emails - exact same logic as TypeScript
            if (addCustomerMailsAndPhones.getCompanyEmails() != null) {
                for (String email : addCustomerMailsAndPhones.getCompanyEmails()) {
                    CompanyEmail companyEmail = new CompanyEmail();
                    companyEmail.setCompanyId(addCustomerMailsAndPhones.getCompanyId());
                    companyEmail.setEmail(email);
                    companyEmail.setCreatedAt(Instant.now());
                    companyEmail.setUpdatedAt(Instant.now());

                    // Skip duplicates - equivalent to skipDuplicates: true
                    if (companyEmailRepository.existsCompanyEmailsByCompany_IdAndEmail(
                            addCustomerMailsAndPhones.getCompanyId(),
                            email)) {
                        companyEmailRepository.save(companyEmail);
                    }
                }
            }

            // Create customer phones - exact same logic as TypeScript
            if (addCustomerMailsAndPhones.getCustomerPhones() != null) {
                for (String phone : addCustomerMailsAndPhones.getCustomerPhones()) {
                    CustomerPhone customerPhone = new CustomerPhone();
                    customerPhone.setPhoneNumber(phone);
                    customerPhone.setCustomerId(addCustomerMailsAndPhones.getCustomerId());
                    customerPhone.setPhoneType(CustomerPhone.Type.PHONE);
                    customerPhone.setCreatedAt(Instant.now());
                    customerPhone.setUpdatedAt(Instant.now());

                    // Skip duplicates - equivalent to skipDuplicates: true
                    if (!customerPhoneRepository.existsCustomerPhoneByCustomer_IdAndPhoneNumber(addCustomerMailsAndPhones.getCustomerId(),
                                                                                                phone)) {
                        customerPhoneRepository.save(customerPhone);
                    }
                }
            }
        } catch (Exception error) {
            log.error("CompanyService/add customer phones and company emails - ", error);
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "An error occurred on the server. Please contact support.");
        }
    }

    /**
     * Find all companies.
     * Equivalent to TypeScript: findAll()
     *
     * @return List of all companies
     */
    public List<Company> findAll() {
        return companyRepository.findAll();
    }

    /**
     * Find company by ID with user permission validation.
     * Equivalent to TypeScript: findOne(id, user)
     *
     * @param id   Company ID
     * @param user Authenticated user
     * @return Company with relationships
     */
    public Company findOne(Integer id,
                           AuthenticatedUser user) {
        validatingUserPermissionCompany(id, user);

        Optional<Company> companyOpt = companyRepository.findByIdWithRelations(id);
        return companyOpt.orElse(null);
    }

    /**
     * Find company by partner ID with user permission validation.
     * Equivalent to TypeScript: findOneByPartner(partner_id, user)
     *
     * @param partnerId Partner ID (kept same variable name as TypeScript)
     * @param user      Authenticated user
     * @return Company with relationships
     */
    public Company findOneByPartner(Integer partnerId,
                                    AuthenticatedUser user) {
        Optional<Company> companyOpt = companyRepository.findByPartnerIdAndDeletedAtIsNull(partnerId);

        if (companyOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
        }

        Company company = companyOpt.get();
        validatingUserPermissionCompany(company.getId(), user);

        return company;
    }

    /**
     * Find customer Monday by ID.
     * Equivalent to TypeScript: findOneCustomerMondayById(id)
     *
     * @param id Customer ID
     */
    public void findOneCustomerMondayById(Integer id) {
        try {
            String crmUrl = crmApiUrl + "/customers/" + id;

            // This is equivalent to the axios.get call in TypeScript
            log.info("TODO: Implement HTTP call to CRM URL: {} for customer ID: {}", crmUrl, id);

            // Original TypeScript had headers:
            // [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            // [HEADER_USER_ROLE]: Role.SYSTEM,

        } catch (Exception error) {
            log.error("CompanyService/findOneCustomerMondayById - ", error);
        }
    }

    /**
     * Find company by name with user permission validation.
     * Equivalent to TypeScript: findOneByName(name, user)
     *
     * @param name Company name
     * @param user Authenticated user
     * @return Company with relationships
     * @ts-legacy not used in the final tag
     */
    public Company findOneByName(String name,
                                 AuthenticatedUser user) {
        Optional<Company> companyOpt = companyRepository.findByName(name);

        if (companyOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
        }

        Company company = companyOpt.get();
        validatingUserPermissionCompany(company.getId(), user);

        return company;
    }

    /**
     * Create a new company.
     * Equivalent to TypeScript: create(data)
     *
     * @param data CreateCompanyDto with company information
     * @return Created company with relationships
     */
    @Transactional
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public Company create(CreateCompanyDto data) {
        // Validation: A company cannot have both VAT and TIN - exact same logic as TypeScript
        if (data.getVat() != null && !data.getVat().isEmpty() && data.getTin() != null && !data.getTin().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "A company can not have both vat and tax number");
        }

        // Check if document is already used - exact same logic as TypeScript
        Company documentAlreadyUsed = findByDocument(data.getVat(), data.getTin());
        if (documentAlreadyUsed != null) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Document already used by another company");
        }

        // Check LUCID number if provided - exact same logic as TypeScript
        if (data.getLucid() != null && !data.getLucid().isEmpty()) {
            Company companyWithLucidAlreadyExists = findByLucid(data.getLucid());
            if (companyWithLucidAlreadyExists != null) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "LUCID number is already in use");
            }
        }

        String relationName = getRelationName(data);

        // Create company billing if provided
        CompanyBilling companyBilling = null;
        if (data.getBilling() != null) {
            companyBilling = new CompanyBilling();
            companyBilling.setFullName(StringUtils.isNotBlank(data.getBilling().getFullName()) ? data.getBilling()
                    .getFullName() : relationName);
            companyBilling.setCountryCode(data.getBilling().getCountryCode());
            companyBilling.setCountryName(data.getBilling().getCountryName());
            companyBilling.setCompanyName(data.getBilling().getCompanyName());
            companyBilling.setStreetAndNumber(data.getBilling().getStreetAndNumber());
            companyBilling.setCity(data.getBilling().getCity());
            companyBilling.setZipCode(data.getBilling().getZipCode());
            companyBilling.setCreatedAt(Instant.now());
            companyBilling.setUpdatedAt(Instant.now());
            companyBilling.setIsCustom(data.getBilling() != null);
            companyBilling = companyBillingRepository.saveAndFlush(companyBilling);
        }

        // Create company address
        CompanyAddress companyAddress = null;
        if (data.getAddress() != null) {
            companyAddress = new CompanyAddress();
            companyAddress.setCountryCode(data.getAddress().getCountryCode());
            companyAddress.setAddressLine(data.getAddress().getAddressLine());
            companyAddress.setCity(data.getAddress().getCity());
            companyAddress.setZipCode(data.getAddress().getZipCode());
            companyAddress.setStreetAndNumber(data.getAddress().getStreetAndNumber());
            companyAddress.setAdditionalAddress(data.getAddress().getAdditionalAddress());
            companyAddress.setCreatedAt(Instant.now());
            companyAddress.setUpdatedAt(Instant.now());
            companyAddress = companyAddressRepository.saveAndFlush(companyAddress);
        }

        // Create company contact
        CompanyContact companyContact = null;
        if (data.getContact() != null) {
            companyContact = new CompanyContact();
            companyContact.setName(data.getContact().getName());
            companyContact.setEmail(data.getContact().getEmail());
            companyContact.setPhoneMobile(data.getContact().getPhoneMobile());
            companyContact.setCreatedAt(Instant.now());
            companyContact.setUpdatedAt(Instant.now());
            companyContact = companyContactRepository.saveAndFlush(companyContact);
        }

        // Create the main company entity - exact same field mapping as TypeScript
        Company company = new Company();
        company.setName(data.getName());
        company.setDescription(data.getDescription());
        company.setTin(data.getTin() != null && !data.getTin().isEmpty() ? data.getTin() : null);
        company.setVat(data.getVat() != null && !data.getVat().isEmpty() ? data.getVat() : null);
        company.setLucid(data.getLucid() != null && !data.getLucid().isEmpty() ? data.getLucid() : null);
        company.setStarting(data.getStarting());
        company.setWebsite(data.getWebsite());
        company.setCreatedAt(Instant.now());
        company.setUpdatedAt(Instant.now());
        company.setDeletedAt(null);
        company.setAddress(companyAddress);
        company.setBilling(companyBilling);
        company.setContacts(companyContact);

        // Set customer relationship if provided - exact same logic as TypeScript
        if (data.getCustomerId() != null) {
            Optional<Customer> customerOpt = customerRepository.findById(data.getCustomerId());
            customerOpt.ifPresent(company::setCustomer);
        }

        // Set partner relationship if provided - exact same logic as TypeScript
        if (data.getPartnerId() != null) {
            Optional<Partner> partnerOpt = partnerRepository.findById(data.getPartnerId());
            partnerOpt.ifPresent(company::setPartner);
        }

        // Save the company
        Company createdCompany = companyRepository.saveAndFlush(company);

        // Set the company reference in contact and billing
        if (companyContact != null) {
            companyContact.setCompanyId(createdCompany.getId());
            companyContactRepository.saveAndFlush(companyContact);
        }

        if (companyBilling != null) {
            companyBilling.setCompany(createdCompany);
            companyBillingRepository.saveAndFlush(companyBilling);
        }

        if (companyAddress != null) {
            List<Company> companies = companyAddress.getCompanies();
            companies.add(company);
            companyAddress.setCompanies(companies);
            companyAddressRepository.saveAndFlush(companyAddress);
        }


        // Create company emails if provided - exact same logic as TypeScript
        List<CompanyEmail> createdEmails = new ArrayList<>();
        if (data.getEmails() != null && !data.getEmails().isEmpty()) {
            for (String email : data.getEmails()) {
                CompanyEmail companyEmail = new CompanyEmail();
                companyEmail.setCompany(createdCompany);
                companyEmail.setEmail(email);
                companyEmail.setCreatedAt(Instant.now());
                companyEmail.setUpdatedAt(Instant.now());

                // Skip duplicates - equivalent to skipDuplicates: true
                if (!companyEmailRepository.existsCompanyEmailsByCompany_IdAndEmail(createdCompany.getId(), email)) {
                    CompanyEmail savedEmail = companyEmailRepository.saveAndFlush(companyEmail);
                    createdEmails.add(savedEmail);
                }
            }
        }
        createdCompany.setEmails(createdEmails);

        // Customer.io and Monday.com integration - exact same logic as TypeScript
        if (createdCompany.getCustomer() != null && createdCompany.getCustomer().getId() != null) {
            customerIoService.updateCompanyByCustomerId(createdCompany.getCustomer().getId());

            mondayService.updateBoardAccountsMonday(createdCompany, createdCompany.getCustomer().getId());
        }

        return createdCompany;
    }

    /**
     * Update an existing company.
     * Equivalent to TypeScript: update(id, data, user)
     * Uses transaction management exactly like TypeScript $transaction
     *
     * @param id   Company ID
     * @param data UpdateCompanyDto with updated information
     * @param user Authenticated user
     * @return Updated company with relationships
     * @ts-legacy remove some unused code and warn
     */
    @Transactional
    @SuppressWarnings("java:S3776")
    public Company update(Integer id,
                          UpdateCompanyDto data,
                          AuthenticatedUser user) {
        validatingUserPermissionCompany(id, user);

        // Find existing company - exact same logic as TypeScript
        Optional<Company> companyOpt = companyRepository.findById(id);
        if (companyOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
        }
        Company company = companyOpt.get();

        // Validation: A company cannot have both VAT and TIN - exact same logic as TypeScript
        if (data.getVat() != null && data.getTin() != null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "A company can not have both vat and tax number");
        }

        // Check if document is already used by another company - exact same logic as TypeScript
        Company documentAlreadyUsed = findByDocument(data.getVat(), data.getTin());
        if (documentAlreadyUsed != null && !documentAlreadyUsed.getId().equals(company.getId())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Document already used by another company");
        }

        // Update company emails if provided - exact same logic as TypeScript
        List<CompanyEmail> updatedEmails = new ArrayList<>();
        if (data.getEmails() != null && !data.getEmails().isEmpty()) {
            // Delete existing emails - exact same as TypeScript: deleteMany
            companyEmailRepository.deleteCompanyEmailByCompany_Id(company.getId());

            // Create new emails - exact same as TypeScript: createMany
            for (String email : data.getEmails()) {
                CompanyEmail companyEmail = new CompanyEmail();
                companyEmail.setCompanyId(company.getId());
                companyEmail.setEmail(email);
                companyEmail.setCreatedAt(Instant.now());
                companyEmail.setUpdatedAt(Instant.now());
                CompanyEmail savedEmail = companyEmailRepository.saveAndFlush(companyEmail);
                updatedEmails.add(savedEmail);
            }
        }
        company.setEmails(updatedEmails);

        // Update company contact if provided - exact same logic as TypeScript
        if (data.getContact() != null) {
            // Delete existing contact - exact same as TypeScript: deleteMany
            companyContactRepository.deleteByCompany_Id(company.getId());

            // Create new contact - exact same as TypeScript: create
            CompanyContact companyContact = new CompanyContact();
            companyContact.setName(data.getContact().getName());
            companyContact.setEmail(data.getContact().getEmail());
            companyContact.setPhoneMobile(data.getContact().getPhoneMobile());
            companyContact.setCompanyId(company.getId());
            companyContact.setCreatedAt(Instant.now());
            companyContact.setUpdatedAt(Instant.now());
            companyContactRepository.save(companyContact);
        }

        // Update company billing if provided - exact same logic as TypeScript
        if (data.getBilling() != null && company.getBilling() != null) {
            CompanyBilling billing = company.getBilling();
            if (data.getBilling().getFullName() != null) {
                billing.setFullName(data.getBilling().getFullName());
            }
            if (data.getBilling().getCountryCode() != null) {
                billing.setCountryCode(data.getBilling().getCountryCode());
            }
            if (data.getBilling().getCountryName() != null) {
                billing.setCountryName(data.getBilling().getCountryName());
            }
            if (data.getBilling().getCompanyName() != null) {
                billing.setCompanyName(data.getBilling().getCompanyName());
            }
            if (data.getBilling().getStreetAndNumber() != null) {
                billing.setStreetAndNumber(data.getBilling().getStreetAndNumber());
            }
            if (data.getBilling().getCity() != null) {
                billing.setCity(data.getBilling().getCity());
            }
            if (data.getBilling().getZipCode() != null) {
                billing.setZipCode(data.getBilling().getZipCode());
            }
            billing.setIsCustom(true);
            billing.setUpdatedAt(Instant.now());
            companyBillingRepository.save(billing);
        }

        // Update main company fields - exact same field mapping as TypeScript
        // TypeScript sets fields directly without null checks
        company.setName(data.getName());
        company.setDescription(data.getDescription());
        company.setTin(data.getTin());
        company.setVat(data.getVat());
        company.setLucid(data.getLucid());
        company.setStarting(data.getStarting());
        company.setWebsite(data.getWebsite());
        company.setUpdatedAt(Instant.now());
        company.setDeletedAt(null);

        // Update company address if provided - exact same logic as TypeScript
        if (data.getAddress() != null && company.getAddress() != null) {
            CompanyAddress address = company.getAddress();
            // TypeScript spreads the address object: ...data.address
            if (data.getAddress().getCountryCode() != null) {
                address.setCountryCode(data.getAddress().getCountryCode());
            }
            if (data.getAddress().getAddressLine() != null) {
                address.setAddressLine(data.getAddress().getAddressLine());
            }
            if (data.getAddress().getCity() != null) {
                address.setCity(data.getAddress().getCity());
            }
            if (data.getAddress().getZipCode() != null) {
                address.setZipCode(data.getAddress().getZipCode());
            }
            if (data.getAddress().getStreetAndNumber() != null) {
                address.setStreetAndNumber(data.getAddress().getStreetAndNumber());
            }
            if (data.getAddress().getAdditionalAddress() != null) {
                address.setAdditionalAddress(data.getAddress().getAdditionalAddress());
            }
            address.setUpdatedAt(Instant.now());
            companyAddressRepository.save(address);
            company.setAddress(address);
        }

        Company updatedCompany = companyRepository.save(company);

        // Customer.io integration - exact same logic as TypeScript
        if (updatedCompany.getCustomer() != null && updatedCompany.getCustomer().getId() != null) {
            customerIoService.updateCompanyByCustomerId(updatedCompany.getCustomer().getId());
        }

        mondayService.updateBoardAccountsMonday(
                updatedCompany,
                updatedCompany.getCustomer() != null ? updatedCompany.getCustomer()
                        .getId() : null);

        // Email notification - exact same logic as TypeScript
        try {
            EmailMessage message = new EmailMessage(
                    "27", user.getEmail(), "<EMAIL>",
                    "Company information changed",
                    objectMapper.convertValue(
                            updatedCompany, new TypeReference<>() {
                            }));
            if (updatedCompany.getCustomer() != null && updatedCompany.getCustomer().getId() != null) {
                message.setRecipientName(
                        updatedCompany.getCustomer().getFirstName(),
                        updatedCompany.getCustomer().getLastName());
            }
            emailOutboxGateway.sendEmail(message);
            log.info("TODO: Send email notification to {} about company information change", user.getEmail());
        } catch (Exception error) {
            log.error("Error sending email notification: ", error);
        }

        return updatedCompany;
    }

    /**
     * Remove (delete) a company.
     * Equivalent to TypeScript: remove(id, user)
     *
     * @param id   Company ID
     * @param user Authenticated user
     */
    @Transactional
    public void remove(Integer id,
                       AuthenticatedUser user) {
        validatingUserPermissionCompany(id, user);

        // Delete the company - exact same logic as TypeScript
        companyRepository.deleteById(id);
    }

    /**
     * Validate VAT ID.
     * Equivalent to TypeScript: findByVatId(data)
     *
     * @param data VatIdDto with VAT information to validate
     * @return VatValidationResult with validation results
     */
    public VatValidationResult findByVatId(VatIdDto data) {
        // Basic validation - exact same logic as TypeScript
        if (data.getVatId() == null || data.getVatId().length() < 2) {
            return VatValidationResult.builder().isValid(false).data(data).error(INVALID).build();
        }

        String vatId = data.getVatId().trim().toUpperCase();
        String vatCountryCode = vatId.substring(0, 2);

        // Check if country code contains digits - exact same logic as TypeScript
        if (vatCountryCode.matches(".*\\d.*")) {
            return VatValidationResult.builder().isValid(false).data(data).error(INVALID).build();
        }

        // Check if VAT country code matches provided country code - exact same logic as TypeScript
        if (!vatCountryCode.equals(data.getCountryCode())) {
            return VatValidationResult.builder().isValid(false).data(data).error(INVALID).build();
        }

        // Check master VAT ID - exact same logic as TypeScript
        if (masterVatId != null && !masterVatId.isEmpty() && vatId.equals(masterVatId)) {
            return VatValidationResult.builder().message("VALID").isValid(true).data(data).build();
        }

        // German VAT validation using VIES - exact same logic as TypeScript
        if ("DE".equals(vatCountryCode)) {
            return vatValidationService.validateGermanVat(data);
        }

        // Foreign VAT validation using EVATR - exact same logic as TypeScript
        return vatValidationService.validateForeignVat(data);
    }

    /**
     * Validate user permission for company access.
     * Equivalent to TypeScript: validatingUserPermissionCompany(id, user)
     *
     * @param id   Company ID
     * @param user Authenticated user
     */
    private void validatingUserPermissionCompany(Integer id,
                                                 AuthenticatedUser user) {
        Optional<Company> companyOpt = companyRepository.findByIdWithRelations(id);

        if (companyOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
        }

        Company company = companyOpt.get();
        Customer customer = company.getCustomer();

        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        // Check customer permission - exact same logic as TypeScript
        if (Role.CUSTOMER.equals(user.getRole()) && !customer.getUserId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this company");
        }
    }

    /**
     * get relation name by customer or partner id
     *
     * @param data the request createCompanyDto
     * @return customer or partner name
     */
    private String getRelationName(CreateCompanyDto data) {
        if (data.getCustomerId() == null && data.getPartnerId() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Customer or partner is required");
        }
        if (data.getCustomerId() != null) {
            Customer customer = customerRepository.findById(data.getCustomerId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found"));
            return customer.getFirstName() + " " + customer.getLastName();
        }
        Partner partner = partnerRepository.findById(data.getPartnerId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Partner not found"));
        return partner.getFirstName() + " " + partner.getLastName();
    }

}
