package de.interzero.oneepr.customer.customer_commitment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.service_setup.dto.FormattedCommitmentDto;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupCommitmentResponseDto;
import de.interzero.oneepr.customer.customer_commitment.dto.AnsweredCommitment;
import de.interzero.oneepr.customer.customer_commitment.dto.CommitmentSubmissionResponse;
import de.interzero.oneepr.customer.customer_commitment.dto.CreateCustomerCommitmentDto;
import de.interzero.oneepr.customer.customer_commitment.dto.UpdateCustomerCommitmentDto;
import org.mapstruct.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Mapper for the CustomerCommitment entity.
 */
@Mapper(componentModel = "spring")
public interface CustomerCommitmentMapper {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget CustomerCommitment commitment,
                       UpdateCustomerCommitmentDto dto);

    /**
     * Updates a {@link CustomerCommitment} entity from the initial creation DTO and the submission response from an external API.
     * This is used for the upsert logic in the create method.
     */
    @Mapping(
            target = "commitment",
            source = "submission.commitment",
            qualifiedByName = "answeredItemsToMap"
    )
    @Mapping(
            source = "createDto.customerEmail",
            target = "customerEmail"
    )
    @Mapping(
            source = "createDto.countryCode",
            target = "countryCode"
    )
    @Mapping(
            source = "submission.year",
            target = "year"
    )
    @Mapping(
            target = "isLicenseRequired",
            source = "isLicenseRequired"
    )
    @Mapping(
            target = "serviceSetup",
            expression = "java(objectToMap(submission.getSetup()))"
    )
    @Mapping(
            target = "blame",
            expression = "java(new java.util.HashMap<>())"
    )
    @Mapping(
            target = "id",
            ignore = true
    )
    @Mapping(
            target = "createdAt",
            ignore = true
    )
    @Mapping(
            target = "updatedAt",
            ignore = true
    )
    @Mapping(
            target = "deletedAt",
            ignore = true
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    @Mapping(
            target = "shoppingCart",
            ignore = true
    )
    @Mapping(
            target = "shoppingCartId",
            source = "createDto.shoppingCartId"
    )
    void updateFromSubmission(@MappingTarget CustomerCommitment entity,
                              CreateCustomerCommitmentDto createDto,
                              CommitmentSubmissionResponse submission,
                              boolean isLicenseRequired);

    /**
     * Updates a {@link CustomerCommitment} entity from the initial creation DTO and the submission response from an external API.
     * This is used for the upsert logic in the create method.
     */
    @Mapping(
            target = "commitment",
            source = "submission.commitment"
    )
    @Mapping(
            source = "createDto.customerEmail",
            target = "customerEmail"
    )
    @Mapping(
            source = "createDto.countryCode",
            target = "countryCode"
    )
    @Mapping(
            source = "submission.year",
            target = "year"
    )
    @Mapping(
            target = "isLicenseRequired",
            source = "isLicenseRequired"
    )
    @Mapping(
            target = "serviceSetup",
            expression = "java(objectToMap(submission.getSetup()))"
    )
    @Mapping(
            target = "blame",
            expression = "java(new java.util.HashMap<>())"
    )
    @Mapping(
            target = "id",
            ignore = true
    )
    @Mapping(
            target = "createdAt",
            ignore = true
    )
    @Mapping(
            target = "updatedAt",
            ignore = true
    )
    @Mapping(
            target = "deletedAt",
            ignore = true
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    @Mapping(
            target = "shoppingCart",
            ignore = true
    )
    @Mapping(
            target = "shoppingCartId",
            source = "createDto.shoppingCartId"
    )
    void updateFromSubmission(@MappingTarget CustomerCommitment entity,
                              CreateCustomerCommitmentDto createDto,
                              ServiceSetupCommitmentResponseDto submission,
                              boolean isLicenseRequired);

    /**
     * Custom mapping method to convert a list of answered commitment items into a map.
     * This method defines the specific business logic for the transformation.
     * Here, we use the criteria ID as the key and the answer as the value.
     *
     * @param items The list of answered items from the DTO.
     * @return A map suitable for the JSONB entity field.
     */
    @Named("answeredItemsToMap")
    default Map<String, Object> answeredItemsToMap(List<AnsweredCommitment> items) {
        if (items == null) {
            return new HashMap<>();
        }
        return items.stream()
                .collect(Collectors.toMap(item -> String.valueOf(item.getId()), AnsweredCommitment::getAnswer));
    }

    /**
     * Generic helper to convert any object to a Map, used for complex jsonb fields.
     */
    default Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(
                obj, new TypeReference<>() {
                });
    }
}
