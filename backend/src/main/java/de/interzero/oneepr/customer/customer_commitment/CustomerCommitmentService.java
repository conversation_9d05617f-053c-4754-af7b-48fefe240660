package de.interzero.oneepr.customer.customer_commitment;

import de.interzero.oneepr.admin.service_setup.dto.CommitmentPackagingServiceDto;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupCommitmentResponseDto;
import de.interzero.oneepr.admin.service_setup.dto.SubmitCommitmentDto;
import de.interzero.oneepr.common.service.AdminInterfaceService;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_commitment.dto.*;
import de.interzero.oneepr.customer.http.AdminInterface;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCartRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Service class for managing CustomerCommitment entities.
 */
@Service
@RequiredArgsConstructor
public class CustomerCommitmentService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCommitmentService.class);

    private final CustomerCommitmentRepository customerCommitmentRepository;

    private final CustomerCommitmentMapper customerCommitmentMapper;

    private final ShoppingCartRepository shoppingCartRepository;

    private final AdminInterfaceService adminInterfaceService;

    /**
     * Creates or updates a customer commitment.
     * <p>
     * This method orchestrates the process of submitting commitment data to an external admin service,
     * then uses the response to create or update a local CustomerCommitment record.
     *
     * @param createDto The DTO containing the initial commitment data.
     * @return The persisted CustomerCommitment entity.
     * @throws ResponseStatusException if input validation fails or the external service call fails.
     * @ts-legacy This method directly translates the logic from the NestJS `create` method,
     * including the initial validation checks, the payload transformation for the external API,
     * the use of a placeholder interface for the HTTP call, and the final upsert logic.
     */
    @Transactional
    public CustomerCommitment create(CreateCustomerCommitmentDto createDto) {

        if (createDto.getYear() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Year is required");
        }

        if (CollectionUtils.isEmpty(createDto.getCommitmentAnswers())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Commitment answers are invalid");
        }

        if (createDto.getShoppingCartId() != null) {
            shoppingCartRepository.findByIdAndDeletedAtIsNull(createDto.getShoppingCartId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Shopping cart not found"));
        }
        SubmitCommitmentDto submitCommitmentDto = new SubmitCommitmentDto();
        submitCommitmentDto.setYear(createDto.getYear());
        submitCommitmentDto.setCommitment(createDto.getCommitmentAnswers().stream().map(answer -> {
            SubmitCommitmentDto.CommitmentAnswerDto commitmentAnswerDto = new SubmitCommitmentDto.CommitmentAnswerDto();
            commitmentAnswerDto.setId(answer.getCriteriaId());
            commitmentAnswerDto.setAnswer(answer.getAnswer());
            return commitmentAnswerDto;
        }).toList());

        ServiceSetupCommitmentResponseDto commitmentSubmission = adminInterfaceService.submitServiceSetupCommitment(
                createDto.getCountryCode(),
                submitCommitmentDto);
        boolean isLicenseRequired = commitmentSubmission.getSetup()
                .getPackagingServices()
                .stream()
                .anyMatch(CommitmentPackagingServiceDto::isObliged);

        CustomerCommitment commitmentToSave = customerCommitmentRepository.findByCustomerEmailAndCountryCodeAndYear(
                createDto.getCustomerEmail(),
                createDto.getCountryCode(),
                Integer.parseInt(commitmentSubmission.getYear())).orElse(new CustomerCommitment());

        customerCommitmentMapper.updateFromSubmission(
                commitmentToSave,
                createDto,
                commitmentSubmission,
                isLicenseRequired);

        return customerCommitmentRepository.save(commitmentToSave);
    }

    /**
     * Updates an existing customer commitment after validating user permissions.
     * This method performs a partial update, only changing the fields provided in the DTO.
     *
     * @param id        The ID of the customer commitment to update.
     * @param updateDto A DTO containing the fields to be updated.
     * @param user      The authenticated user, for permission checking.
     * @return The updated CustomerCommitment entity.
     * @throws ResponseStatusException if the commitment is not found or the user lacks permission.
     */
    @Transactional
    public CustomerCommitment update(Integer id,
                                     UpdateCustomerCommitmentDto updateDto,
                                     AuthenticatedUser user) {
        validatingUserPermissionCustomerCommitment(id, user);

        CustomerCommitment existingCommitment = customerCommitmentRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "CustomerCommitment not found with ID: " + id));

        customerCommitmentMapper.partialUpdate(existingCommitment, updateDto);

        return customerCommitmentRepository.save(existingCommitment);
    }

    /**
     * Finds all customer commitments based on the provided query filters.
     *
     * @param query DTO containing filter criteria.
     * @return A list of CustomerCommitment entities.
     */
    @Transactional(readOnly = true)
    public List<CustomerCommitment> findAll(FindCustomerCommitmentDto query) {
        if (query.getCustomerEmail() == null || query.getCustomerEmail().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Customer email is required");
        }
        if (query.getYear() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Year is required");
        }

        if (query.getCountryCode() != null && !query.getCountryCode().isEmpty()) {
            return customerCommitmentRepository.findAllByCustomerEmailAndYearAndCountryCodeAndDeletedAtIsNull(
                    query.getCustomerEmail(),
                    query.getYear(),
                    query.getCountryCode());
        } else {
            return customerCommitmentRepository.findAllByCustomerEmailAndYearAndDeletedAtIsNull(
                    query.getCustomerEmail(),
                    query.getYear());
        }
    }

    /**
     * Finds a single customer commitment by its ID, after validating user permission.
     *
     * @param id   The ID of the customer commitment.
     * @param user The authenticated user.
     * @return The found CustomerCommitment entity.
     */
    @Transactional(readOnly = true)
    public CustomerCommitment findOne(Integer id,
                                      AuthenticatedUser user) {
        return validatingUserPermissionCustomerCommitment(id, user);
    }


    /**
     * Soft deletes a customer commitment by setting its 'deletedAt' field.
     *
     * @param id   The ID of the customer commitment to remove.
     * @param user The authenticated user.
     * @return The updated (soft-deleted) CustomerCommitment entity.
     */
    @Transactional
    public CustomerCommitment remove(Integer id,
                                     AuthenticatedUser user) {
        CustomerCommitment commitment = validatingUserPermissionCustomerCommitment(id, user);
        commitment.setDeletedAt(LocalDate.now());
        return customerCommitmentRepository.save(commitment);
    }

    /**
     * Validates if the authenticated user has permission to access/modify the customer commitment.
     *
     * @param id   The ID of the customer commitment.
     * @param user The authenticated user.
     * @return The fetched CustomerCommitment entity if validation passes.
     */
    private CustomerCommitment validatingUserPermissionCustomerCommitment(Integer id,
                                                                          AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid CustomerCommitment ID");
        }
        CustomerCommitment commitment = customerCommitmentRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer commitment not found"));
        if (user.getRole() == Role.CUSTOMER) {
            Customer customer = commitment.getCustomer();
            if (customer == null || customer.getUserId() == null) {
                logger.warn(
                        "Attempt by CUSTOMER role user ({}) to access commitment ({}) with no associated customer.",
                        user.getId(),
                        id);
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "Cannot verify permission for this customer commitment.");
            }
            int userIdFromAuth;
            try {
                userIdFromAuth = Integer.parseInt(user.getId());
            } catch (NumberFormatException e) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "User ID format is invalid.", e);
            }

            if (!Objects.equals(customer.getUserId(), userIdFromAuth)) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have permission to access this customer commitment");
            }
        }
        return commitment;
    }
}
