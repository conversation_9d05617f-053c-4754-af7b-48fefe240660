package de.interzero.oneepr.admin.criteria;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.dto.CreateCriteriaDto;
import de.interzero.oneepr.admin.criteria.dto.UpdateCriteriaDto;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSection;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSectionRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.string.Api;
import jakarta.persistence.EntityManager;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Integration tests for the {@link CriteriaController}.
 * This class validates the full HTTP request-response cycle, including security,
 * data validation, and database interactions for the Criteria module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CriteriaControllerTest {

    private static final String API_URL = Api.CRITERIAS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CriteriaRepository criteriaRepository;

    @Autowired
    private CriteriaOptionRepository criteriaOptionRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ObligationCheckSectionRepository obligationCheckSectionRepository;

    private Country testCountry;
    private ObligationCheckSection testSection;
    private Criteria testCriteria;

    private Criteria conditionalCriteria; // For testing conditional links

    private PackagingService testPackagingService; // To link to options

    /**
     * Sets up a consistent database state before each test runs by creating
     * all necessary prerequisite entities. This data is automatically rolled back
     * by the {@code @Transactional} annotation after each test.
     */
    @BeforeEach
    void setUp() {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        testCountry = createAndSaveTestCountry();
        testSection = createAndSaveTestSection(testCountry);
        testPackagingService = createAndSaveTestPackagingService(testCountry);

        testCriteria = new Criteria();
        testCriteria.setCountry(testCountry);
        testCriteria.setObligationCheckSection(testSection);
        testCriteria.setPackagingService(testPackagingService);
        testCriteria.setMode(Criteria.Mode.COMMITMENT);
        testCriteria.setType(Criteria.Type.PACKAGING_SERVICE);
        testCriteria.setTitle("Select a Packaging Service");
        testCriteria.setInputType(Criteria.InputType.RADIO);
        testCriteria = criteriaRepository.save(testCriteria);

        // Create a criteria that can be used as a conditional follow-up question
        conditionalCriteria = new Criteria();
        conditionalCriteria.setCountry(testCountry);
        conditionalCriteria.setMode(Criteria.Mode.COMMITMENT);
        conditionalCriteria.setType(Criteria.Type.REPORT_SET);
        conditionalCriteria.setTitle("This is a conditional question");
        conditionalCriteria = criteriaRepository.save(conditionalCriteria);

        CriteriaOption option = new CriteriaOption();
        option.setCriteria(testCriteria);
        option.setOptionValue("Option A");
        option.setValue("Value A");
        option.setCreatedAt(Instant.now());
        option.setUpdatedAt(Instant.now());
        option.setOptionalCriteria(conditionalCriteria);
        testCriteria.addOption(option);
        criteriaOptionRepository.save(option);
    }

    /**
     * Verifies that a POST request with a complex DTO successfully creates a new Criteria
     * and its associated options, including nested relationships.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewCriteriaAndOptionsWithNestedRelations() throws Exception {
        CreateCriteriaDto createDto = getCreateCriteriaDto();

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.title", is("Use Authorized Representative?")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())))
                .andExpect(jsonPath("$.obligation_check_section_id", is(testSection.getId().intValue())))
                .andExpect(jsonPath("$.options", hasSize(1)))
                .andExpect(jsonPath("$.options[0].packaging_services", hasSize(1)))
                .andExpect(jsonPath("$.options[0].packaging_services[0].id", is(testPackagingService.getId())))
                .andExpect(jsonPath("$.options[0].conditional_criteria_id", is(conditionalCriteria.getId())));
    }

    /**
     * Verifies that a GET request returns only the criteria associated with a specific section ID.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAllWithSectionId_shouldReturnOnlyCriteriaForThatSection() throws Exception {
        // Act & Assert
        mockMvc.perform(get(API_URL + "/section/{id}", testSection.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testCriteria.getId())))
                .andExpect(jsonPath("$[0].title", is("Select a Packaging Service")));
    }


    /**
     * Helper method to create a comprehensive {@link CreateCriteriaDto} for testing.
     * This DTO includes nested options with their own relationship IDs to test complex creation logic.
     *
     * @return A non-null, fully populated instance of {@code CreateCriteriaDto}.
     */
    private @NotNull CreateCriteriaDto getCreateCriteriaDto() {
        CreateCriteriaDto.CriteriaOptionDto optionDto = new CreateCriteriaDto.CriteriaOptionDto();
        optionDto.setOptionValue("Yes");
        optionDto.setValue("1");
        optionDto.setConditionalCriteriaId(conditionalCriteria.getId()); // Link to conditional question
        optionDto.setPackagingServiceIds(List.of(testPackagingService.getId())); // Link to packaging service

        CreateCriteriaDto createDto = new CreateCriteriaDto();
        createDto.setCountryId(testCountry.getId());
        createDto.setObligationCheckSectionId(testSection.getId());
        createDto.setMode(Criteria.Mode.CALCULATOR);
        createDto.setType(Criteria.Type.AUTHORIZE_REPRESENTATIVE);
        createDto.setTitle("Use Authorized Representative?");
        createDto.setInputType(Criteria.InputType.YES_NO);
        createDto.setOptions(Collections.singletonList(optionDto));
        return createDto;
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnAllActiveCriteria() throws Exception {
        mockMvc.perform(get(API_URL)).andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct Criteria entity.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnCorrectCriteria_whenFound() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", testCriteria.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCriteria.getId())))
                .andExpect(jsonPath("$.title", is("Select a Packaging Service")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingCriteria() throws Exception {
        UpdateCriteriaDto updateDto = new UpdateCriteriaDto();
        updateDto.setTitle("New Updated Title");
        updateDto.setHelpText("This is new help text.");
        updateDto.setCountryId(testCountry.getId());
        mockMvc.perform(put(API_URL + "/{id}", testCriteria.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCriteria.getId())));
    }

    // --- Helper Methods for Test Setup (Corrected) ---

    /**
     * Finds and returns the 'DE' Country entity, creating and persisting it only if it does not already exist.
     * This method is idempotent and safe to call multiple times within a test suite.
     *
     * @return The persisted Country entity.
     */
    private Country createAndSaveTestCountry() {
        // First, attempt to find the country by its unique code.
        Optional<Country> existingCountry = countryRepository.findByCode("DE");

        // If the country is present, return it immediately without performing any writes.
        if (existingCountry.isPresent()) {
            return existingCountry.get();
        }

        // If the country is NOT present, create, configure, and save a new one.
        Country newCountry = new Country();
        newCountry.setName("Germany");
        newCountry.setCode("DE");
        newCountry.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(newCountry);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Standard Service");
        service.setDescription("Standard packaging service description.");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ObligationCheckSection createAndSaveTestSection(Country country) {
        ObligationCheckSection section = new ObligationCheckSection();
        section.setTitle("Test Section");
        section.setDisplayOrder(1);
        section.setCountry(country);
        return obligationCheckSectionRepository.saveAndFlush(section);
    }
}