package de.interzero.oneepr.admin.mail;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for {@link SandboxEmailGateway}.
 * <p>
 * This test sends a real email via local Mailpit and is disabled by default.
 * Run manually when needed.
 */
@Disabled("Disabled by default. Run manually when testing local email delivery.")
@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest
class SandboxEmailGatewayIntegrationTest {

    @Autowired
    private EmailOutboxGateway emailOutboxGateway;

    @Test
    void shouldActuallySendEmail(CapturedOutput output) {
        EmailMessage message = new EmailMessage(
                "template-real-test",
                "<EMAIL>", "<EMAIL>", "<PERSON>",
                "Integration Test Subject",
                Map.of("user", "Jane Doe", "code", "123456"));

        emailOutboxGateway.sendEmail(message);

        assertThat(output).contains("Sandbox email sent to: <EMAIL>")
                .contains("Transactional Message ID: template-real-test")
                .contains(" - user: Jane Doe")
                .contains(" - code: 123456");
    }
}

