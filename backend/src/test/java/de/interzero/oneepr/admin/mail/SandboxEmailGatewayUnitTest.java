package de.interzero.oneepr.admin.mail;

import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * Unit test for {@link SandboxEmailGateway}.
 */
@ExtendWith(OutputCaptureExtension.class)
class SandboxEmailGatewayUnitTest {

    private SandboxEmailGateway sandboxEmailGateway;

    private JavaMailSender mailSender;

    @BeforeEach
    void setUp() {
        mailSender = mock(JavaMailSender.class);
        MimeMessage mimeMessage = mock(MimeMessage.class);
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);

        sandboxEmailGateway = new SandboxEmailGateway(mailSender);
    }

    @Test
    void shouldLogEmailData_whenSendEmailCalled(CapturedOutput output) {
        EmailMessage message = new EmailMessage(
                "template-123",
                                                "<EMAIL>", "<EMAIL>", "John",
                                                "Test Subject",
                                                Map.of("username", "John", "link", "https://example.com/reset"));

        sandboxEmailGateway.sendEmail(message);

        assertThat(output).contains("Sandbox email sent to: <EMAIL>")
                .contains("Transactional Message ID: template-123")
                .contains(" - username: John")
                .contains(" - link: https://example.com/reset");

        verify(mailSender).createMimeMessage();
        verify(mailSender).send(any(MimeMessage.class));
    }

    @Test
    void shouldThrowEmailDeliveryException_whenSendingFails() {
        MimeMessage mimeMessage = mock(MimeMessage.class);
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new MailSendException("SMTP failure")).when(mailSender).send(any(MimeMessage.class));

        EmailMessage message = new EmailMessage(
                "template-error",
                                                "<EMAIL>", "<EMAIL>", "John",
                                                "Failure Subject",
                                                Map.of("error", "simulate failure"));

        EmailDeliveryException exception = assertThrows(
                EmailDeliveryException.class,
                () -> sandboxEmailGateway.sendEmail(message));

        assertThat(exception).hasMessageContaining("Failed to send sandbox email");
        assertThat(exception.getCause()).isInstanceOf(MailSendException.class);

        verify(mailSender).send(any(MimeMessage.class));
    }

}
