package de.interzero.oneepr.admin.upload_data;

import de.interzero.oneepr.admin.entity.Broker;
import de.interzero.oneepr.admin.entity.BrokerCompany;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.PriceListRepository;
import de.interzero.oneepr.admin.upload_data.constants.ImportFieldConstants;
import de.interzero.oneepr.common.service.AwsService;
import de.interzero.oneepr.common.string.TestRole;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.time.Instant;

import static de.interzero.oneepr.common.string.Api.UPLOAD_DATA;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive unit tests for {@link UploadDataController} with 100% coverage.
 * Uses mocked AWS SDK for external service mocking and MockMvc for HTTP testing.
 */
@SpringBootTest
@AutoConfigureMockMvc
class UploadDataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private BrokerCompanyRepository brokerCompanyRepository;

    @Autowired
    private PriceListRepository priceListRepository;

    @Autowired
    private BrokerRepository brokerRepository;

    @MockBean
    private AwsService awsService;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Override AWS S3 configuration for tests
        registry.add("aws.region", () -> "eu-west-2");
        registry.add("aws.s3.bucket-name", () -> "test-bucket");
    }

    // Test data
    private MockMultipartFile validOrderExcelFile;

    private MockMultipartFile validCompanyExcelFile;

    @BeforeEach
    @Transactional
    void setUp() {
        // Setup test data in database
        setupTestData();

        // Setup test files
        setupTestFiles();

        // Setup default AWS SDK mocks
        setupDefaultAwsMocks();
    }

    private void setupTestFiles() {
        try {
            // Create valid order Excel file with proper headers
            validOrderExcelFile = createMockOrderExcelFile();

            // Create valid company Excel file with proper headers
            validCompanyExcelFile = createMockCompanyExcelFile();

        } catch (IOException e) {
            throw new RuntimeException("Failed to setup test files", e);
        }
    }

    /**
     * Creates a mock Excel file with proper order headers and sample data
     */
    private MockMultipartFile createMockOrderExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row with order headers
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {
            ImportFieldConstants.REGISTRATION_NUMBER,
            ImportFieldConstants.YEAR,
            ImportFieldConstants.TYPE,
            ImportFieldConstants.GLASS,
            ImportFieldConstants.PAPER_PPK,
            ImportFieldConstants.FERROUS_METALS,
            ImportFieldConstants.ALUMINIUM_COMPOSITES,
            ImportFieldConstants.LIQUID_COMPOSITES,
            ImportFieldConstants.OTHER_PPK_COMPOSITES,
            ImportFieldConstants.PLASTICS_COMPOSITES,
            ImportFieldConstants.OTHER_MATERIALS
        };

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create sample data row
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456"); // Registration Number
        dataRow.createCell(1).setCellValue(2024); // Year
        dataRow.createCell(2).setCellValue("INITIAL_REPORT"); // Type
        dataRow.createCell(3).setCellValue(100.5); // Glass
        dataRow.createCell(4).setCellValue(200.0); // Paper PPK
        dataRow.createCell(5).setCellValue(50.0); // Ferrous Metals
        dataRow.createCell(6).setCellValue(75.5); // Aluminium Composites
        dataRow.createCell(7).setCellValue(25.0); // Liquid Composites
        dataRow.createCell(8).setCellValue(30.0); // Other PPK Composites
        dataRow.createCell(9).setCellValue(150.0); // Plastics Composites
        dataRow.createCell(10).setCellValue(40.0); // Other Materials

        // Convert to byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
            "file",
            "test-order-data.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );
    }

    /**
     * Creates a mock Excel file with proper company headers and sample data
     */
    private MockMultipartFile createMockCompanyExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Companies");

        // Create header row with company headers
        Row headerRow = sheet.createRow(0);
        String[] companyHeaders = {
            ImportFieldConstants.COMPANY_NAME,
            ImportFieldConstants.REGISTRATION_NUMBER,
            ImportFieldConstants.VAT,
            ImportFieldConstants.TAX,
            ImportFieldConstants.COUNTRY_CODE,
            ImportFieldConstants.ADDRESS_STREET,
            ImportFieldConstants.ADDRESS_NUMBER,
            ImportFieldConstants.CITY,
            ImportFieldConstants.CONTACT_EMAIL,
            ImportFieldConstants.CONTACT_NAME,
            ImportFieldConstants.PHONE_NUMBER
        };

        for (int i = 0; i < companyHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(companyHeaders[i]);
        }

        // Create sample data row
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("Test Company GmbH"); // Company Name
        dataRow.createCell(1).setCellValue("REG123456"); // Registration Number
        dataRow.createCell(2).setCellValue("DE123456789"); // VAT
        dataRow.createCell(3).setCellValue(""); // TAX (empty as VAT is filled)
        dataRow.createCell(4).setCellValue("DE"); // Country Code
        dataRow.createCell(5).setCellValue("Musterstraße"); // Address Street
        dataRow.createCell(6).setCellValue("123"); // Address Number
        dataRow.createCell(7).setCellValue("Berlin"); // City
        dataRow.createCell(8).setCellValue("<EMAIL>"); // Contact Email
        dataRow.createCell(9).setCellValue("John Doe"); // Contact Name
        dataRow.createCell(10).setCellValue("+49 30 12345678"); // Phone Number

        // Convert to byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-company-data.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with valid fraction weights for getFractions testing
     */
    private MockMultipartFile createOrderFileWithValidFractions() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with valid fraction weights
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue(150.75); // Glass
        dataRow.createCell(4).setCellValue(250.50); // Paper PPK
        dataRow.createCell(5).setCellValue(75.25); // Ferrous Metals
        dataRow.createCell(6).setCellValue(100.00); // Aluminium Composites
        dataRow.createCell(7).setCellValue(50.50); // Liquid Composites
        dataRow.createCell(8).setCellValue(80.75); // Other PPK Composites
        dataRow.createCell(9).setCellValue(200.25); // Plastics Composites
        dataRow.createCell(10).setCellValue(60.00); // Other Materials

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-valid-fractions.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with zero fraction weights
     */
    private MockMultipartFile createOrderFileWithZeroWeights() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with zero weights
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue(0.0); // Glass
        dataRow.createCell(4).setCellValue(0.0); // Paper PPK
        dataRow.createCell(5).setCellValue(0.0); // Ferrous Metals
        dataRow.createCell(6).setCellValue(0.0); // Aluminium Composites
        dataRow.createCell(7).setCellValue(0.0); // Liquid Composites
        dataRow.createCell(8).setCellValue(0.0); // Other PPK Composites
        dataRow.createCell(9).setCellValue(0.0); // Plastics Composites
        dataRow.createCell(10).setCellValue(0.0); // Other Materials

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-zero-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with negative fraction weights
     */
    private MockMultipartFile createOrderFileWithNegativeWeights() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with negative weights
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue(-50.0); // Glass - negative
        dataRow.createCell(4).setCellValue(-100.0); // Paper PPK - negative
        dataRow.createCell(5).setCellValue(-25.0); // Ferrous Metals - negative
        dataRow.createCell(6).setCellValue(75.5); // Aluminium Composites - positive
        dataRow.createCell(7).setCellValue(-10.0); // Liquid Composites - negative
        dataRow.createCell(8).setCellValue(30.0); // Other PPK Composites - positive
        dataRow.createCell(9).setCellValue(-150.0); // Plastics Composites - negative
        dataRow.createCell(10).setCellValue(40.0); // Other Materials - positive

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-negative-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with invalid weight types (strings)
     */
    private MockMultipartFile createOrderFileWithInvalidWeightTypes() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with invalid weight types
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue("invalid"); // Glass - string
        dataRow.createCell(4).setCellValue("not_a_number"); // Paper PPK - string
        dataRow.createCell(5).setCellValue(""); // Ferrous Metals - empty string
        dataRow.createCell(6).setCellValue(75.5); // Aluminium Composites - valid number
        dataRow.createCell(7).setCellValue("N/A"); // Liquid Composites - string
        dataRow.createCell(8).setCellValue(30.0); // Other PPK Composites - valid number
        dataRow.createCell(9).setCellValue("abc123"); // Plastics Composites - string
        dataRow.createCell(10).setCellValue(40.0); // Other Materials - valid number

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-invalid-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with decimal fraction weights
     */
    private MockMultipartFile createOrderFileWithDecimalWeights() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with precise decimal weights
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue(123.456789); // Glass - high precision decimal
        dataRow.createCell(4).setCellValue(0.001); // Paper PPK - very small decimal
        dataRow.createCell(5).setCellValue(999.999); // Ferrous Metals - decimal close to integer
        dataRow.createCell(6).setCellValue(50.5); // Aluminium Composites - simple decimal
        dataRow.createCell(7).setCellValue(0.123456); // Liquid Composites - small decimal
        dataRow.createCell(8).setCellValue(1000.001); // Other PPK Composites - large decimal
        dataRow.createCell(9).setCellValue(75.75); // Plastics Composites - quarter decimal
        dataRow.createCell(10).setCellValue(33.333333); // Other Materials - repeating decimal

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-decimal-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with very large fraction weights
     */
    private MockMultipartFile createOrderFileWithLargeWeights() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with very large weights
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow.createCell(3).setCellValue(1000000.0); // Glass - 1 million
        dataRow.createCell(4).setCellValue(999999999.99); // Paper PPK - close to billion
        dataRow.createCell(5).setCellValue(500000.5); // Ferrous Metals - half million
        dataRow.createCell(6).setCellValue(1234567.89); // Aluminium Composites - large decimal
        dataRow.createCell(7).setCellValue(10000000.0); // Liquid Composites - 10 million
        dataRow.createCell(8).setCellValue(750000.25); // Other PPK Composites - 3/4 million
        dataRow.createCell(9).setCellValue(2000000.0); // Plastics Composites - 2 million
        dataRow.createCell(10).setCellValue(100000000.0); // Other Materials - 100 million

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-large-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with empty/null weights
     */
    private MockMultipartFile createOrderFileWithEmptyWeights() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create data row with empty weights (cells will be empty/null)
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("REG123456");
        dataRow.createCell(1).setCellValue(2024);
        dataRow.createCell(2).setCellValue("INITIAL_REPORT");
        // Leave fraction cells empty - they will be null/empty
        dataRow.createCell(3); // Glass - empty cell
        dataRow.createCell(4); // Paper PPK - empty cell
        dataRow.createCell(5); // Ferrous Metals - empty cell
        dataRow.createCell(6); // Aluminium Composites - empty cell
        dataRow.createCell(7); // Liquid Composites - empty cell
        dataRow.createCell(8); // Other PPK Composites - empty cell
        dataRow.createCell(9); // Plastics Composites - empty cell
        dataRow.createCell(10); // Other Materials - empty cell

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-empty-weights.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    /**
     * Creates a mock Excel file with multiple order rows
     */
    private MockMultipartFile createOrderFileWithMultipleOrders() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Orders");

        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] orderHeaders = {ImportFieldConstants.REGISTRATION_NUMBER, ImportFieldConstants.YEAR, ImportFieldConstants.TYPE, ImportFieldConstants.GLASS, ImportFieldConstants.PAPER_PPK, ImportFieldConstants.FERROUS_METALS, ImportFieldConstants.ALUMINIUM_COMPOSITES, ImportFieldConstants.LIQUID_COMPOSITES, ImportFieldConstants.OTHER_PPK_COMPOSITES, ImportFieldConstants.PLASTICS_COMPOSITES, ImportFieldConstants.OTHER_MATERIALS};

        for (int i = 0; i < orderHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(orderHeaders[i]);
        }

        // Create first order row
        Row dataRow1 = sheet.createRow(1);
        dataRow1.createCell(0).setCellValue("REG123456");
        dataRow1.createCell(1).setCellValue(2024);
        dataRow1.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow1.createCell(3).setCellValue(100.0); // Glass
        dataRow1.createCell(4).setCellValue(200.0); // Paper PPK
        dataRow1.createCell(5).setCellValue(50.0); // Ferrous Metals
        dataRow1.createCell(6).setCellValue(75.0); // Aluminium Composites
        dataRow1.createCell(7).setCellValue(25.0); // Liquid Composites
        dataRow1.createCell(8).setCellValue(30.0); // Other PPK Composites
        dataRow1.createCell(9).setCellValue(150.0); // Plastics Composites
        dataRow1.createCell(10).setCellValue(40.0); // Other Materials

        // Create second order row
        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(0).setCellValue("REG789012");
        dataRow2.createCell(1).setCellValue(2024);
        dataRow2.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow2.createCell(3).setCellValue(150.5); // Glass
        dataRow2.createCell(4).setCellValue(300.25); // Paper PPK
        dataRow2.createCell(5).setCellValue(75.75); // Ferrous Metals
        dataRow2.createCell(6).setCellValue(100.5); // Aluminium Composites
        dataRow2.createCell(7).setCellValue(50.25); // Liquid Composites
        dataRow2.createCell(8).setCellValue(80.0); // Other PPK Composites
        dataRow2.createCell(9).setCellValue(200.75); // Plastics Composites
        dataRow2.createCell(10).setCellValue(60.5); // Other Materials

        // Create third order row
        Row dataRow3 = sheet.createRow(3);
        dataRow3.createCell(0).setCellValue("REG345678");
        dataRow3.createCell(1).setCellValue(2024);
        dataRow3.createCell(2).setCellValue("INITIAL_REPORT");
        dataRow3.createCell(3).setCellValue(75.25); // Glass
        dataRow3.createCell(4).setCellValue(125.0); // Paper PPK
        dataRow3.createCell(5).setCellValue(25.5); // Ferrous Metals
        dataRow3.createCell(6).setCellValue(50.75); // Aluminium Composites
        dataRow3.createCell(7).setCellValue(15.25); // Liquid Composites
        dataRow3.createCell(8).setCellValue(20.0); // Other PPK Composites
        dataRow3.createCell(9).setCellValue(100.5); // Plastics Composites
        dataRow3.createCell(10).setCellValue(30.25); // Other Materials

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
                "file",
                "test-multiple-orders.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray());
    }

    private void setupDefaultAwsMocks() {
        try {
            // Default successful response for AwsService presigned URL
            AwsService.PresignedUrlResponse mockResponse = new AwsService.PresignedUrlResponse("https://test-bucket.s3.amazonaws.com/upload",
                                                                                               new java.util.HashMap<>());
            when(awsService.generatePresignedUploadUrl(any(String.class), any(String.class))).thenReturn(mockResponse);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup AWS mocks", e);
        }
    }

    /**
     * Sets up test data in the database for getFractions testing
     */
    private void setupTestData() {
        // Clear existing data to ensure clean state
        brokerCompanyRepository.deleteAll();
        priceListRepository.deleteAll();
        brokerRepository.deleteAll();

        // Create test broker
        Broker testBroker = new Broker();
        testBroker.setName("Test Broker");
        testBroker.setEmail("<EMAIL>");
        testBroker.setPhone("+49 30 12345678");
        testBroker.setEnroledAt(Instant.now());
        testBroker.setCompanyName("Test Broker Company");
        testBroker.setVat("DE123456789");
        testBroker.setCreatedAt(Instant.now());
        testBroker.setIsActive(true);
        testBroker.setUserId(1001);
        testBroker = brokerRepository.save(testBroker);

        // Create test broker company with registration number REG123456
        BrokerCompany testCompany = createTestBrokerCompany(
                testBroker,
                "Test Company GmbH",
                "REG123456",
                "DE123456789",
                "Berlin");
        brokerCompanyRepository.save(testCompany);

        // Create additional test companies for multiple order tests
        BrokerCompany testCompany2 = createTestBrokerCompany(
                testBroker,
                "Test Company 2 GmbH",
                "REG789012",
                "DE789012345",
                "Munich");
        brokerCompanyRepository.save(testCompany2);

        BrokerCompany testCompany3 = createTestBrokerCompany(
                testBroker,
                "Test Company 3 GmbH",
                "REG345678",
                "DE345678901",
                "Hamburg");
        brokerCompanyRepository.save(testCompany3);

        // Create test price list for year 2024
        PriceList testPriceList = new PriceList();
        testPriceList.setType(PriceList.Type.DIRECT_LICENSE);
        testPriceList.setName("Test Direct License 2024");
        testPriceList.setDescription("Test price list for 2024");
        testPriceList.setStartDate(Instant.parse("2024-01-01T00:00:00Z"));
        testPriceList.setEndDate(Instant.parse("2024-12-31T23:59:59Z"));
        testPriceList.setBasicPrice(100);
        testPriceList.setMinimumPrice(50);
        testPriceList.setRegistrationFee(25);
        testPriceList.setHandlingFee(10);
        testPriceList.setVariableHandlingFee(0.05);
        testPriceList.setPrice(200);
        testPriceList.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        testPriceList.setConditionTypeValue("2024");
        testPriceList.setCreatedAt(Instant.now());
        testPriceList.setUpdatedAt(Instant.now());

        // Create thresholds JSON with fraction values for getFractions testing
        String thresholdsJson = """
                [
                    {
                        "title": "Standard Threshold",
                        "value": 1000,
                        "helper_text": "Up to 1000 units",
                        "fractions": {
                            "A7B2X": {"code": "A7B2X", "name": "Glass", "value": 0.15},
                            "K9P4M": {"code": "K9P4M", "name": "Paper / Paperboard / Cardboard (PPK)", "value": 0.12},
                            "W3N8L": {"code": "W3N8L", "name": "Ferrous Metals", "value": 0.08},
                            "R5T9V": {"code": "R5T9V", "name": "Aluminium incl. Composites", "value": 0.20},
                            "H6Y4Z": {"code": "H6Y4Z", "name": "Liquid Composites", "value": 0.25},
                            "Q2C7D": {"code": "Q2C7D", "name": "Other Composites based on PPK", "value": 0.18},
                            "J8F3S": {"code": "J8F3S", "name": "Plastics incl. Composites", "value": 0.22},
                            "M1G5B": {"code": "M1G5B", "name": "Other Materials", "value": 0.10}
                        }
                    }
                ]
                """;
        testPriceList.setThresholds(thresholdsJson);

        priceListRepository.save(testPriceList);
    }

    /**
     * Helper method to create a BrokerCompany entity with common fields
     */
    private BrokerCompany createTestBrokerCompany(Broker broker,
                                                  String name,
                                                  String registerNumber,
                                                  String vat,
                                                  String city) {
        BrokerCompany company = new BrokerCompany();
        // Manually assign ID for test environment to avoid sequence issues
        company.setId(registerNumber.hashCode() & Integer.MAX_VALUE); // Use hashCode to generate unique ID
        company.setBroker(broker);
        company.setName(name);
        company.setRegisterNumber(registerNumber);
        company.setVat(vat);
        company.setCountryCode("DE");
        company.setAddressStreet("Test Street");
        company.setAddressNumber("123");
        company.setCity(city);
        company.setContactName("Test Contact");
        company.setContactEmail("test@" + registerNumber.toLowerCase() + ".com");
        company.setPhoneNumber("+49 30 12345678");
        company.setCreatedAt(Instant.now());
        return company;
    }

    // ========== Tests for findAll endpoint ==========

    /**
     * Test for {@link UploadDataController#findAll(String)} - successful case
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenValidId() throws Exception {
        // Given
        String brokerId = "1";

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", brokerId).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - valid numeric ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenValidNumericId() throws Exception {
        // Given
        String brokerId = "123";

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", brokerId).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }



    /**
     * Test for {@link UploadDataController#findAll(String)} - missing ID parameter
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnBadRequest_whenMissingIdParameter() throws Exception {
        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest()); // Missing required parameter
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - empty ID parameter
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnBadRequest_whenEmptyIdParameter() throws Exception {
        // Given
        String emptyId = null;

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", emptyId).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest()); // NumberFormatException causes 500
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - zero ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenZeroId() throws Exception {
        // Given
        String zeroId = "0";

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", zeroId).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - negative ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenNegativeId() throws Exception {
        // Given
        String negativeId = "-1";

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", negativeId).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    // ========== Tests for preview endpoint ==========

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - successful order preview
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnOrderPreview_whenValidOrderFile() throws Exception {
        // Given
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - successful company preview
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnCompanyPreview_whenValidCompanyFile() throws Exception {
        // Given
        String type = "company";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validCompanyExcelFile)
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - null file
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldThrowException_whenNullFile() throws Exception {
        // Given
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest()); // Missing required file parameter
    }


    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - missing type parameter
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnBadRequest_whenMissingTypeParameter() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest()); // Missing required parameter
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - invalid type parameter
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnError_whenInvalidTypeParameter() throws Exception {
        // Given
        String invalidType = "invalid";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", invalidType)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest()); // Service should throw BadRequest for invalid type
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - case insensitive type
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnPreview_whenCaseInsensitiveType() throws Exception {
        // Given
        String upperCaseType = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", upperCaseType)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    // ========== AWS SDK Integration Tests ==========

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - AWS S3 service exception
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldHandleServiceException_whenAwsServiceException() throws Exception {
        // Given
        when(awsService.generatePresignedUploadUrl(
                any(String.class),
                any(String.class))).thenThrow(new RuntimeException(
                "AWS service error"));

        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk()); // Service should handle exceptions gracefully
    }


    // ========== Security and Authorization Tests ==========

    /**
     * Test for {@link UploadDataController#findAll(String)} - unauthorized access
     */
    @Test
    void findAll_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", "1").accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - unauthorized access
     */
    @Test
    void preview_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", "order")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isUnauthorized());
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - different user roles
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void findAll_shouldReturnForbidden_whenCustomerRole() throws Exception {
        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", "1").accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - different user roles
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void preview_shouldReturnForbidden_whenCustomerRole() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", "order")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - SUPER_ADMIN role access
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnUploadData_whenSuperAdminRole() throws Exception {
        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", "1").accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - SUPER_ADMIN role access
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void preview_shouldReturnPreview_whenSuperAdminRole() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", "order")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    // ========== Edge Cases and Boundary Tests ==========

    /**
     * Test for {@link UploadDataController#findAll(String)} - maximum integer value
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenMaxIntegerValue() throws Exception {
        // Given
        String maxIntValue = String.valueOf(Integer.MAX_VALUE);

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", maxIntValue).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#findAll(String)} - minimum integer value
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnUploadData_whenMinIntegerValue() throws Exception {
        // Given
        String minIntValue = String.valueOf(Integer.MIN_VALUE);

        // When & Then
        mockMvc.perform(get(UPLOAD_DATA).param("id", minIntValue).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }




    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - type parameter with whitespace
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldHandleWhitespace_whenTypeHasWhitespace() throws Exception {
        // Given
        String typeWithWhitespace = " order ";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validOrderExcelFile)
                        .param("type", typeWithWhitespace)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest()); // Service should handle trimming
    }

    // ========== Additional Tests with Proper Headers ==========

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - order file with proper headers validation
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldValidateOrderHeaders_whenOrderFileWithCorrectHeaders() throws Exception {
        // Given
        extractedPreview("order", validOrderExcelFile);
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - company file with proper headers validation
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldValidateCompanyHeaders_whenCompanyFileWithCorrectHeaders() throws Exception {
        // Given
        extractedPreview("company", validCompanyExcelFile);
    }

    private void extractedPreview(String type,
                                  MockMultipartFile file) throws Exception {
        // When & Then - Should succeed because file has all required company headers
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(file)
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - wrong file type for order
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldReturnError_whenCompanyFileUsedForOrderType() throws Exception {
        // Given - Using company file for order type (header mismatch)
        String type = "order";

        // When & Then - Should fail because company headers don't match order requirements
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview")
                        .file(validCompanyExcelFile)
                        .param("type", type)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest()); // Should fail header validation
    }

    // ========== Tests for getFractions Function Coverage ==========

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with valid numeric weights
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldProcessFractions_whenValidNumericWeights() throws Exception {
        // Given - Create Excel file with valid numeric weights for all fractions
        MockMultipartFile validFractionsFile = createOrderFileWithValidFractions();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(validFractionsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[0].fractions[0].code").exists())
                .andExpect(jsonPath("$[0].fractions[0].name").exists())
                .andExpect(jsonPath("$[0].fractions[0].weight").exists())
                .andExpect(jsonPath("$[0].fractions[0].value").exists());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with zero weights
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldProcessFractions_whenZeroWeights() throws Exception {
        // Given - Create Excel file with zero weights
        MockMultipartFile zeroWeightsFile = createOrderFileWithZeroWeights();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(zeroWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with negative weights
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldHandleErrors_whenNegativeWeights() throws Exception {
        // Given - Create Excel file with negative weights
        MockMultipartFile negativeWeightsFile = createOrderFileWithNegativeWeights();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(negativeWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].errors").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[0].fractions[?(@.error)]").exists());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with invalid weight types
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldHandleErrors_whenInvalidWeightTypes() throws Exception {
        // Given - Create Excel file with string weights
        MockMultipartFile invalidWeightsFile = createOrderFileWithInvalidWeightTypes();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(invalidWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].errors").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[0].fractions[?(@.error)]").exists());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with decimal weights
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldProcessFractions_whenDecimalWeights() throws Exception {
        // Given - Create Excel file with decimal weights
        MockMultipartFile decimalWeightsFile = createOrderFileWithDecimalWeights();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(decimalWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[0].fractions[0].weight").isNumber());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with large numbers
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldProcessFractions_whenLargeWeights() throws Exception {
        // Given - Create Excel file with very large weights
        MockMultipartFile largeWeightsFile = createOrderFileWithLargeWeights();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(largeWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with empty/null weights
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldHandleErrors_whenEmptyWeights() throws Exception {
        // Given - Create Excel file with empty/null weights
        MockMultipartFile emptyWeightsFile = createOrderFileWithEmptyWeights();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(emptyWeightsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[0].errors").isArray());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions with multiple orders
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldProcessFractions_whenMultipleOrders() throws Exception {
        // Given - Create Excel file with multiple order rows
        MockMultipartFile multipleOrdersFile = createOrderFileWithMultipleOrders();
        String type = "order";

        // When & Then
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(multipleOrdersFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3)) // Should have 3 orders
                .andExpect(jsonPath("$[0].fractions").isArray())
                .andExpect(jsonPath("$[1].fractions").isArray())
                .andExpect(jsonPath("$[2].fractions").isArray());
    }

    /**
     * Test for {@link UploadDataController#preview(MultipartFile, String)} - getFractions fraction code mapping
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void preview_shouldMapFractionCodes_whenValidFractions() throws Exception {
        // Given - Create Excel file with valid fractions
        MockMultipartFile validFractionsFile = createOrderFileWithValidFractions();
        String type = "order";

        // When & Then - Verify that fraction codes are correctly mapped
        mockMvc.perform(multipart(UPLOAD_DATA + "/preview").file(validFractionsFile)
                                .param("type", type)
                                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].fractions").isArray())
                // Verify specific fraction codes are present (based on GermanyFraction enum)
                .andExpect(jsonPath("$[0].fractions[?(@.code == 'A7B2X' && @.name == 'Glass')]").exists())
                .andExpect(jsonPath(
                        "$[0].fractions[?(@.code == 'K9P4M' && @.name == 'Paper / Paperboard / Cardboard (PPK)')]").exists())
                .andExpect(jsonPath("$[0].fractions[?(@.code == 'W3N8L' && @.name == 'Ferrous Metals')]").exists())
                .andExpect(jsonPath("$[0].fractions[?(@.code == 'R5T9V' && @.name == 'Aluminium incl. Composites')]").exists());
    }


}
