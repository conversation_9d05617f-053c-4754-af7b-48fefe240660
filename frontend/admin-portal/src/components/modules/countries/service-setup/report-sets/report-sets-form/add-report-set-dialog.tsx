import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createReportSet } from "@/lib/api/report-sets";
import { queryClient } from "@/lib/react-query";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { CreateReportSet, ReportSetMode } from "@/types/service-setup/report-set";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { MouseEvent, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

interface AddReportSetDialogProps {
  packagingService: PackagingService;
}

const addReportSetFormSchema = z.object({
  mode: z
    .enum([ReportSetMode.ON_PLATAFORM, ReportSetMode.BY_EXCEL, ReportSetMode.NO_REPORTING])
    .default(ReportSetMode.ON_PLATAFORM),
});

export type AddReportSetFormData = z.infer<typeof addReportSetFormSchema>;

export function AddReportSetDialog({ packagingService }: AddReportSetDialogProps) {
  const router = useRouter();
  const { country } = useServiceSetup();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { handleSubmit, reset, control } = useForm<AddReportSetFormData>({
    resolver: zodResolver(addReportSetFormSchema),
    defaultValues: {
      mode: ReportSetMode.ON_PLATAFORM,
    },
  });

  const { mutate, isPending: isCreatingReportSet } = useMutation({
    mutationFn: (data: CreateReportSet) => createReportSet(data),
  });

  useEffect(() => {
    reset({
      mode: ReportSetMode.ON_PLATAFORM,
    });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  async function handleFormSubmit(data: AddReportSetFormData) {
    const createData: CreateReportSet = {
      name: packagingService.name,
      packaging_service_id: packagingService.id,
      mode: data.mode,
      type: "FRACTIONS",
      sheet_file_id: null,
      sheet_file_description: null,
    };

    mutate(createData, {
      onSuccess: (response) => {
        queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
        queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

        enqueueSnackbar("Report set created successfully", { variant: "success" });

        router.push(`/en/countries/${country.code}/service-setup/report-set/${response.id}`);
      },
      onError: () => {
        enqueueSnackbar("Failed to create report set", { variant: "error" });
      },
    });
  }

  function handleClickSubmit(e: MouseEvent<HTMLButtonElement>) {
    e.preventDefault();
    handleSubmit(handleFormSubmit)();
  }

  function handleDialogOpenChange(open: boolean) {
    if (!open) reset();

    setIsDialogOpen(open);
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <div>
          <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
            Add fraction set
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="bg-white py-8 sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add a fraction set</DialogTitle>
          <DialogDescription className="text-tonal-dark-cream-20">
            Select the reporting method to create a fraction set.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-10">
          <div className="space-y-4">
            <div className="w-full space-y-4">
              <label className="text-primary text-base font-centra mb-2">Reporting method</label>
              <Controller
                control={control}
                name="mode"
                render={({ field: { onChange, value } }) => (
                  <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)}>
                    <div className="flex flex-col gap-3">
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value={ReportSetMode.ON_PLATAFORM} />
                        Reporting via platform
                      </label>
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value={ReportSetMode.BY_EXCEL} />
                        Reporting via Excel file
                      </label>
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value={ReportSetMode.NO_REPORTING} />
                        No reporting (i.e. flat rates)
                      </label>
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value={ReportSetMode.SALES_PACKAGING} />
                        Sales packaging (DE) *
                      </label>
                      <label className="text-tonal-dark-cream-20 text-base font-centra mt-5">
                        *Fractions are pre-defined.
                      </label>
                    </div>
                  </RadioGroup>
                )}
              />
            </div>
          </div>
          <div className="flex items-center justify-end">
            <Button
              type="button"
              variant="filled"
              color="yellow"
              size="medium"
              disabled={isCreatingReportSet}
              onClick={handleClickSubmit}
            >
              {isCreatingReportSet ? "Saving..." : "Add fraction set"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
