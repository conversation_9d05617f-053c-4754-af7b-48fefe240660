"use client";

import { FullReportSet } from "@/types/service-setup";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useFormContext } from "react-hook-form";
import { ReportSetDelete } from "../components/report-set-delete";
import { ReportSetFormData } from "../components/report-set-form-provider";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { ReportSetPriceLists } from "../components/report-set-price-lists";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { useMutationState } from "@tanstack/react-query";
import { useEffect } from "react";

interface ReportSetPlataformFormProps {
  countryCode: string;
  reportSet: FullReportSet;
  onFormChange?: (hasChanges: boolean) => void;
}

export function ReportSetNoReportingForm({ reportSet, onFormChange }: ReportSetPlataformFormProps) {
  const {
    register,
    formState: { errors, isDirty },
  } = useFormContext<ReportSetFormData>();

  useEffect(() => {
    if (onFormChange) {
      onFormChange(isDirty);
    }
  }, [isDirty, onFormChange]);

  const updateReportSetMutation = useMutationState({
    filters: {
      mutationKey: ["update-report-set", reportSet.id],
      status: "pending",
    },
  });
  function getFormError() {
    const parsedErrors = Object.entries(errors || {});

    const firstError = parsedErrors[0];

    if (!firstError) return null;

    const [key, error] = firstError;

    if (key === "fractions") {
      if (error.root?.message) return `Fractions: ${error.root.message}`;

      if (Array.isArray(error)) return "Fractions: Error in some fraction input";

      return `Fractions: ${error?.message}`;
    }

    if (key === "columns") {
      if (Array.isArray(error)) return "Columns: Error in some column input";

      return `Columns: ${error?.message}`;
    }

    if (key === "price_lists") {
      return `Price Lists: ${error?.message}`;
    }

    return `General: ${error?.message}`;
  }

  const isSubmitting = !!updateReportSetMutation.length;

  return (
    <div className="mt-6 space-y-10">
      <div className="bg-background rounded-[20px] p-8 space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-primary text-lg font-bold">New report set</p>
          <ReportSetDelete reportSetId={reportSet.id} />
        </div>
        <div className="w-full">
          <Input
            label="Fraction set name*"
            placeholder="Name for the Report Set"
            {...register("name")}
            variant={errors.name ? "error" : "default"}
            errorMessage={errors.name?.message}
          />
        </div>
      </div>
      <div className="bg-background rounded-[20px] py-6 px-5 space-y-8 w-full">
        <p className="text-primary text-lg font-bold">PRO pricing</p>
        <ReportSetPriceLists />
      </div>
      {!!getFormError() && (
        <div className="flex items-center gap-2 mb-2">
          <Error className="fill-error size-4" />
          <p className="text-error text-sm text-right">{getFormError()}</p>
        </div>
      )}
      <div className="w-full flex items-center justify-end">
        <Button
          type="submit"
          variant="filled"
          color={!!Object.keys(errors).length ? "red" : "yellow"}
          size="large"
          className="w-60"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Add"}
        </Button>
      </div>
    </div>
  );
}
