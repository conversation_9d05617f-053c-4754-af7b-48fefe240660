import { FractionIcon } from "@/components/ui/fraction-icon";
import { FractionInput } from "@/components/ui/fraction-input";
import { ReportSetColumn } from "@/types/service-setup/report-set-column";
import { ReportSetColumnFraction } from "@/types/service-setup/report-set-column-fraction";
import { ReportSetFraction } from "@/types/service-setup/report-set-fraction";
import { ReactNode } from "react";
import { Question } from "@interzero/oneepr-react-ui";
interface ReportTableProps {
  fractions: (ReportSetFraction & {
    children: (ReportSetFraction & {
      children: ReportSetFraction[];
    })[];
  })[];
  columns: (ReportSetColumn & {
    children: (ReportSetColumn & {
      fractions: ReportSetColumnFraction[];
    })[];
  })[];
  fractionCell?: ReactNode;
}

export function ReportTable({ fractions, columns, fractionCell }: ReportTableProps) {
  return (
    <div className="rounded-[20px] overflow-hidden space-y-[1px]">
      <div className="w-full text-primary text-sm bg-background">
        <div className="flex items-stretch text-sm font-normal gap-[1px]">
          <div className="py-6 px-2 bg-surface-03 w-44"></div>
          {columns.map((column) => (
            <div key={column.code} className="flex-1 py-4 px-2 bg-surface-03">
              {column.name}
            </div>
          ))}
        </div>
        <div className="flex flex-col gap-[1px]">
          {fractions.map((firstLevelFraction) => (
            <>
              <div className="flex items-stretch w-full gap-[1px]">
                <div className="bg-[#A9C8FF] p-2 w-44 text-base overflow-hidden text-ellipsis whitespace-nowrap">
                  {firstLevelFraction.name}
                </div>
                {columns.map((column) => (
                  <div key={column.code} className="bg-[#A9C8FF] p-2 flex-1"></div>
                ))}
              </div>
              {!firstLevelFraction.has_second_level && (
                <>
                  <ReportTableSecondColumns columns={columns} />
                  <ReportTableDeclareRow fraction={firstLevelFraction} columns={columns} fractionCell={fractionCell} />
                </>
              )}
              {firstLevelFraction.has_second_level && (
                <>
                  {firstLevelFraction.children.map((secondLevelFraction) => (
                    <>
                      <ReportTableSecondColumns
                        columns={columns}
                        secondLevelFractionName={
                          firstLevelFraction.has_third_level ? secondLevelFraction.name : undefined
                        }
                      />
                      {firstLevelFraction.has_third_level &&
                        secondLevelFraction.children.map((thirdLevelFraction) => (
                          <ReportTableDeclareRow
                            key={thirdLevelFraction.code}
                            fraction={thirdLevelFraction}
                            columns={columns}
                            fractionCell={fractionCell}
                          />
                        ))}
                      {!firstLevelFraction.has_third_level && (
                        <ReportTableDeclareRow
                          fraction={secondLevelFraction}
                          columns={columns}
                          fractionCell={fractionCell}
                        />
                      )}
                    </>
                  ))}
                </>
              )}
            </>
          ))}
        </div>
      </div>
    </div>
  );
}

interface ReportTableDeclareRowProps {
  fraction: ReportSetFraction;
  columns: ReportTableProps["columns"];
  fractionCell?: ReactNode;
}

function ReportTableDeclareRow({ fraction, columns, fractionCell }: ReportTableDeclareRowProps) {
  return (
    <div className="flex items-stretch w-full gap-[1px]">
      <div className="bg-surface-03 p-2 w-44 flex items-center gap-2">
        <Question className="size-4" />
        <FractionIcon size="small" iconUrl={fraction.fraction_icon.image_url} />
        {fraction.name}
      </div>
      {columns.map((column) => (
        <div key={column.code} className="flex-1 flex items-stretch gap-[1px]">
          {column.children ? (
            column.children.map((secondLevelColumn) => {
              const disabled = !secondLevelColumn.fractions.find((f) => f.fraction_code === fraction.code);
              return (
                <div key={secondLevelColumn.code} className="flex-1 p-2">
                  {fractionCell || (
                    <FractionInput
                      type="weight"
                      className="h-10 rounded-2xl"
                      placeholder={disabled ? "---" : "kg"}
                      disabled={disabled}
                    />
                  )}
                </div>
              );
            })
          ) : (
            <div key={column.code} className="flex-1 p-2">
              {fractionCell || <FractionInput placeholder="---" type="weight" className="h-10 rounded-2xl" />}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

interface ReportTableSecondColumnsProps {
  columns: ReportTableProps["columns"];
  secondLevelFractionName?: string;
}

function ReportTableSecondColumns({ columns, secondLevelFractionName }: ReportTableSecondColumnsProps) {
  return (
    <div className="flex items-stretch gap-[1px]">
      <div className="bg-[#CCE4FF] p-2 w-44 text-base overflow-hidden text-ellipsis whitespace-nowrap">
        {secondLevelFractionName}
      </div>
      {columns.map((column) => (
        <div key={column.code} className="flex-1 flex items-stretch gap-[1px]">
          {column.children ? (
            column.children.map((secondLevelColumn) => (
              <div key={secondLevelColumn.code} className="flex-1 bg-[#CCE4FF] p-2">
                {secondLevelColumn.name}
              </div>
            ))
          ) : (
            <div key={column.code} className="flex-1 bg-[#CCE4FF] p-2">
              {column.name}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
